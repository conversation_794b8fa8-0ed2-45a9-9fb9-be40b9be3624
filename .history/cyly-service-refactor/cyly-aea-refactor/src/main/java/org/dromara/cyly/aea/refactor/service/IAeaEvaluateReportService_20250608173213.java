/*
 * @Author: 肖剑峰 <EMAIL>
 * @Date: 2025-06-03 16:48:33
 * @LastEditors: 肖剑峰 <EMAIL>
 * @LastEditTime: 2025-06-04 18:57:55
 * @FilePath: \cyly-cloud-refactor\cyly-service-refactor\cyly-aea-refactor\src\main\java\org\dromara\cyly\aea\refactor\service\IAeaEvaluateReportService.java
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
package org.dromara.cyly.aea.refactor.service;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReport;
import org.dromara.cyly.aea.refactor.domain.bo.AeaEvaluateReportBo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 评估报告信息
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface IAeaEvaluateReportService {

    /**
     * 查询评估报告信息
     */
    AeaEvaluateReportVo queryById(Long id);

    /**
     * 查询评估报告信息列表
     */
    TableDataInfo<AeaEvaluateReportVo> queryPageList(AeaEvaluateReportBo bo, PageQuery pageQuery);

    /**
     * 查询评估报告信息列表
     */
    List<AeaEvaluateReportVo> queryList(AeaEvaluateReportBo bo);

    /**
     * 新增评估报告信息
     */
    Boolean insertByBo(AeaEvaluateReportBo bo);

    /**
     * 修改评估报告信息
     */
    Boolean updateByBo(AeaEvaluateReportBo bo);

    /**
     * 校验并批量删除评估报告信息信息
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 创建评估报告
     * @param reportBo 报告业务对象
     * @return 报告ID
     */
    Long createEvaluateReport(AeaEvaluateReportBo reportBo);

    /**
     * 生成PDF报告
     * @param reportId 报告ID
     * @return PDF文件URL
     */
    String generatePdfReport(Long reportId);

    /**
     * 根据任务ID查询评估报告
     * @param taskId 任务ID
     * @return 评估报告
     */
    AeaEvaluateReportVo queryByTaskId(Long taskId);

    /**
     * 更新报告URL
     * @param reportId 报告ID
     * @param reportUrl 报告URL
     * @return 是否成功
     */
    Boolean updateReportUrl(Long reportId, String reportUrl);

    /**
     * 获取报告下载链接
     * @param reportId 报告ID
     * @return 下载链接
     */
    String getReportDownloadUrl(Long reportId);

    List<AeaEvaluateReportVo> selectVoListByTaskIds(Set<Long> taskIdList);

    Long selectCount(String code);
}
