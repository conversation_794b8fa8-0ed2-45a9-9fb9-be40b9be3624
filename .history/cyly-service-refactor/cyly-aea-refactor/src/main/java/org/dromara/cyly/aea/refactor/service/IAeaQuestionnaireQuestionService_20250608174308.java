package org.dromara.cyly.aea.refactor.service;

import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cyly.aea.refactor.domain.bo.AeaQuestionnaireQuestionBo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireQuestionVo;

import java.util.Collection;
import java.util.List;

/**
 * 评估问卷问题Service接口
 *
 * <AUTHOR>
 * @date 2025-05-27
 */
public interface IAeaQuestionnaireQuestionService {

    /**
     * 查询评估问卷问题
     *
     * @param id 主键
     * @return 评估问卷问题
     */
    AeaQuestionnaireQuestionVo queryById(Long id);

    /**
     * 分页查询评估问卷问题列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 评估问卷问题分页列表
     */
    TableDataInfo<AeaQuestionnaireQuestionVo> queryPageList(AeaQuestionnaireQuestionBo bo, PageQuery pageQuery);

    /**
     * 查询符合条件的评估问卷问题列表
     *
     * @param bo 查询条件
     * @return 评估问卷问题列表
     */
    List<AeaQuestionnaireQuestionVo> queryList(AeaQuestionnaireQuestionBo bo);

    /**
     * 新增评估问卷问题
     *
     * @param bo 评估问卷问题
     * @return 是否新增成功
     */
    Boolean insertByBo(AeaQuestionnaireQuestionBo bo);

    /**
     * 修改评估问卷问题
     *
     * @param bo 评估问卷问题
     * @return 是否修改成功
     */
    Boolean updateByBo(AeaQuestionnaireQuestionBo bo);

    /**
     * 批量新增评估问卷问题
     *
     * @param boList 问题业务对象列表
     * @return 是否新增成功
     */
    Boolean insertBatch(List<AeaQuestionnaireQuestionBo> boList);

    /**
     * 校验并批量软删除评估问卷问题信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid);

    /**
     * 根据题目ID列表查询题目信息
     * @param ids 题目ID列表
     * @return 题目列表
     */
    List<AeaQuestionnaireQuestionVo> selectListByIds(List<Long> ids);
}
