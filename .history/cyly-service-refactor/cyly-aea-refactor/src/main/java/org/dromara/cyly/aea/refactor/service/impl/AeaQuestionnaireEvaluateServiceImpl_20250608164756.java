package org.dromara.cyly.aea.refactor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.AbilityLevelEnum;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.enums.SourceEnum;
import org.dromara.common.core.enums.TaskStatusEnum;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.cyly.aea.refactor.domain.*;
import org.dromara.cyly.aea.refactor.domain.bo.*;
import org.dromara.cyly.aea.refactor.domain.vo.*;
import org.dromara.cyly.aea.refactor.mapper.*;
import org.dromara.cyly.aea.refactor.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 问卷评估核心业务服务实现
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AeaQuestionnaireEvaluateServiceImpl implements IAeaQuestionnaireEvaluateService {

    private final IAeaTaskEvaluateService taskService;
    private final IAeaTaskEvaluateInfoService evaluateInfoService;
    private final IAeaTaskEvaluateExecutionRecordService executionRecordService;
    private final IAeaEvaluateReportService evaluateReportService;
    private final IAeaQuestionnaireService questionnaireService;
    private final IAeaQuestionnaireQuestionService questionnaireQuestionService;
    private final AeaQuestionnaireAnswerMapper aeaQuestionnaireAnswerMapper;
    private final AeaTaskEvaluateExecutionRecordMapper aeaTaskEvaluateExecutionRecordMapper;
    private final AeaTaskEvaluateInfoMapper aeaTaskEvaluateInfoMapper;
    private final AeaEvaluateReportMapper aeaEvaluateReportMapper;
    private final AeaElderHealthInfoMapper aeaElderHealthInfoMapper;
    private final AeaQuestionnaireMapper aeaQuestionnaireMapper;
    private final AeaTaskEvaluateMapper aeaTaskEvaluateMapper;


    /**
     * 开始评估，先查询是否有长者的建档信息
     */
    @Override
    public Boolean startEvaluate(Long taskId) {
        //  1. 查询长者的建档信息
        AeaTaskEvaluateVo task = taskService.queryById(taskId);
        // 2. 如果没有长者的建档信息，返回false
        Long rows = aeaElderHealthInfoMapper.selectCount(new QueryWrapper<AeaElderHealthInfo>().eq("aea_elder_id", task.getElderId()));
        if (rows == 0) {
            log.error("未查找到:{}的建档信息", task.getElderId());
            return false;
        }
        // 3. 如果有长者的建档信息，更新任务状态为进行中
        AeaTaskEvaluateBo taskBo = new AeaTaskEvaluateBo();
        taskBo.setId(task.getId());
        taskBo.setStartTime(new Date());
        taskBo.setStatus(TaskStatusEnum.IN_PROGRESS.getCode());
        taskBo.setUpdateTime(new Date());
        //  4. 更新任务
        return taskService.updateByBo(taskBo);
    }

    /**
     * 第一步：创建评估任务流程
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createEvaluateTaskFlow(AeaTaskEvaluateBo aeaTaskEvaluate) {
        try {
            // 1. 创建评估任务
            aeaTaskEvaluate.setSource(SourceEnum.ASSESSOR.getCode());
            aeaTaskEvaluate.setAssignTime(DateUtils.getNowDate());
            aeaTaskEvaluate.setTaskNum(generateReportCode()); // 任务编号
            Long taskId = taskService.createEvaluateTask(aeaTaskEvaluate);
            log.info("创建评估任务成功，任务ID: {}", taskId);

            // 2. 查询评估问卷表，设置评估详情的任务ID
            AeaQuestionnaireBo aeaQuestionnaireBo = new AeaQuestionnaireBo();
            aeaQuestionnaireBo.setType(2);
            List<AeaQuestionnaireVo> questionnaireVoList = questionnaireService.queryList(aeaQuestionnaireBo);
            // 加入需要答题的问卷
            questionnaireVoList.forEach(questionnaireVo -> {
                AeaTaskEvaluateInfoBo evaluateInfoBo = new AeaTaskEvaluateInfoBo();
                evaluateInfoBo.setTaskId(taskId);
                evaluateInfoBo.setQuestionnaireId(questionnaireVo.getId());
                evaluateInfoBo.setStartTime(new Date());
                // 3. 创建评估详情
                Long infoId = evaluateInfoService.createEvaluateInfo(evaluateInfoBo);
                log.info("创建评估详情成功，详情ID: {}", infoId);
            });
            return taskId;
        } catch (Exception e) {
            log.error("创建评估任务流程失败", e);
            throw new RuntimeException("创建评估任务失败，请重新创建！");
        }
    }

    /**
     * 第二步：获取问卷目录结构
     * @param taskId 任务ID
     * @return 任务详情列表
     */
    @Override
    public List<AeaTaskEvaluateInfoVo> getAeaTaskEvaluateInfoList(Long taskId) {
        List<AeaTaskEvaluateInfoVo> infoVoList = new ArrayList<>();
        try {
            AeaTaskEvaluateInfoBo queryBo = new AeaTaskEvaluateInfoBo();
            queryBo.setTaskId(taskId);
            infoVoList = evaluateInfoService.queryList(queryBo);
            log.info("获取任务详情列表成功 · 任务ID: {}, 详情数量: {}", taskId, infoVoList.size());
        } catch (Exception e) {
            log.error("{}:问卷目录不存在！", taskId);
        }
        return infoVoList;
    }

    /**
     * 第三步：提交答案
     */
    @Override
    public Boolean submitAnswers(AeaTaskSubmitAnswerBo bo) {

        List<Long> answerIdList = new ArrayList<>();
        Long infoId = bo.getInfoId();
        Long taskId = bo.getTaskId();
        Long questionnaireId = bo.getQuestionnaireId(); // 问卷id
        List<AeaTaskEvaluateExecutionRecordBo> evaluateExecutionRecordBos = bo.getEvaluateExecutionRecordBos(); // 答卷内容
        log.info("【开始提交答案 · 任务ID: {}，详情ID: {}，问卷ID: {}，问题数量: {}】", taskId, infoId, questionnaireId, evaluateExecutionRecordBos.size());
        // 1. 查询模块目录下的题库数量
        AeaQuestionnaireQuestionBo questionnaireQuestionBo = new AeaQuestionnaireQuestionBo();
        questionnaireQuestionBo.setQuestionnaireId(questionnaireId);
        List<AeaQuestionnaireQuestionVo> aeaQuestionnaireQuestionVos = questionnaireQuestionService.queryList(questionnaireQuestionBo);
        // 2. 提交的答题数量与题库数量进行比对
        if (aeaQuestionnaireQuestionVos.size() != evaluateExecutionRecordBos.size()) {
            log.error("提交失败：提交答案为: {}, 题库数量为: {}", evaluateExecutionRecordBos.size(), aeaQuestionnaireQuestionVos.size());
            throw new RuntimeException("提交失败：请检查是否全部答完！");
        }
        evaluateExecutionRecordBos.forEach(record -> {
            answerIdList.add(record.getAnswerId());
        });
        List<AeaQuestionnaireAnswerVo> answerVos = aeaQuestionnaireAnswerMapper.selectVoByIds(answerIdList);
        // 3. 批量保存答题记录
        List<AeaTaskEvaluateExecutionRecord> evaluateExecutionRecordList = BeanUtil.copyToList(evaluateExecutionRecordBos, AeaTaskEvaluateExecutionRecord.class);
        // 得分初始化
        int score = 0;
        for (AeaQuestionnaireAnswerVo answerVo : answerVos) {
            // 得分累加
            score += answerVo.getScore();
        }
        log.info("【提交答案成功 · 任务ID: {}，详情ID: {}，问卷ID: {}，分数: {}】", taskId, infoId, questionnaireId, score);
        // 批量保存
        aeaTaskEvaluateExecutionRecordMapper.insertBatch(evaluateExecutionRecordList);
        // 更新分数，结束时间，评估状态
        AeaTaskEvaluateInfoBo aeaTaskEvaluateInfoBo = new AeaTaskEvaluateInfoBo();
        aeaTaskEvaluateInfoBo.setId(infoId);
        aeaTaskEvaluateInfoBo.setTotalScore(score);
        aeaTaskEvaluateInfoBo.setEndTime(new Date());
        aeaTaskEvaluateInfoBo.setStatus(TaskStatusEnum.COMPLETED.getCode());
        return evaluateInfoService.updateByBo(aeaTaskEvaluateInfoBo);
    }

    /**
     * 第四步：获取答题结果
     */
    @Override
    public Map<String, Object> getEvaluateReport(Long taskId, Long questionnaireId) {
        // 1. 获取答题结果
        LambdaQueryWrapper<AeaTaskEvaluateInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(taskId != null, AeaTaskEvaluateInfo::getTaskId, taskId);
        queryWrapper.gt(AeaTaskEvaluateInfo::getTotalScore, (0));
        List<AeaTaskEvaluateInfoVo> info = aeaTaskEvaluateInfoMapper.selectVoList(queryWrapper);
        log.info("【获取答题结果成功 · 任务ID: {}，问卷ID: {}，结果数量: {}】", taskId, questionnaireId, info.size());
        if (info.isEmpty()) {
            log.error("【获取答题结果失败 · 任务ID: {}，问卷ID: {}】", taskId, questionnaireId);
            throw new RuntimeException("获取答题结果失败");
        }
        int total = 0;
        for (AeaTaskEvaluateInfoVo infoVo : info) {
            total += infoVo.getTotalScore();
        }
        Map<String, Object> result = new HashMap<>();
        // 获取模块列表详细
        result.put("records", info);
        // 总分
        result.put("total", total);
        if (total >= 90) {
            result.put("CapabilityLevel", AbilityLevelEnum.INTACT.getDescription());
        }
        if (total > 66 && total < 90) {
            result.put("CapabilityLevel", AbilityLevelEnum.MILD_DISABILITY.getDescription());
        }
        if (total > 46 && total <= 66) {
            result.put("CapabilityLevel", AbilityLevelEnum.MODERATE_DISABILITY.getDescription());
        }
        if (total > 30 && total <= 46) {
            result.put("CapabilityLevel", AbilityLevelEnum.SEVERE_DISABILITY.getDescription());
        }
        if (total <= 30) {
            result.put("CapabilityLevel", AbilityLevelEnum.FULL_DISABILITY.getDescription());
        }
        return result;
    }

    /**
     * 第五步：生成PDF报告
     * 完整实现PDF报告生成流程
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateReportPdf(AeaEvaluateReportBo aeaEvaluateReportBo) {
        try {
            log.info("开始第五步：生成PDF报告，任务ID: {}", aeaEvaluateReportBo.getTaskId());

            /*-------------------------第一节PDF数据----------------------------------------*/
            //获取老年人能力评估基本信息表信息列表
            List<AeaQuestionnaireVo> aeaQuestionnaireVoList = aeaQuestionnaireMapper.selectVoList();
            //获取任务记录表->表 A.1 评估信息表所需填充数据
            AeaTaskEvaluateVo aeaTaskEvaluateVo = aeaTaskEvaluateMapper.selectVoById(aeaEvaluateReportBo.getTaskId());
            log.info("查询到评估任务记录表信息: {}", aeaTaskEvaluateVo);
            /*-------------------------第二节PDF数据----------------------------------------*/
            //获取老年人能力评估基本信息表->评估对象基本信息表所需填充数据
            LambdaQueryWrapper<AeaQuestionnaire> aeaQuestionnaireLambdaQueryWrapper = new LambdaQueryWrapper<>();
            //构建老年人能力评估基本信息表questionnaireIdChildrenIds
            List<AeaQuestionnaireVo> questionnaireIdChildrenIds = new ArrayList<>();
            for (AeaQuestionnaireVo vo : aeaQuestionnaireVoList){
                if (vo.getTitle().equals("老年人能力评估基本信息表")){
                    //根据id查询出子节点
                    aeaQuestionnaireLambdaQueryWrapper.eq(AeaQuestionnaire::getParentId, vo.getId());
                    questionnaireIdChildrenIds = aeaQuestionnaireMapper.selectVoList(aeaQuestionnaireLambdaQueryWrapper);
                }
            }
            //对questionnaireIdChildrenIds进行根据sort进行正序排序
            questionnaireIdChildrenIds = questionnaireIdChildrenIds.stream().sorted(Comparator.comparing(AeaQuestionnaireVo::getSort)).toList();
            //获取老年人能力评估基本信息表Children列表
            List<AeaTaskEvaluateExecutionRecordVo> aeaTaskEvaluateExecutionRecordVos;
            //根据问卷问题id获取->老年人能力评估基本信息表问卷问题所答信息(包含：表 A.2 评估对象基本信息表、表 A.3 信息提供者及联系人信息表、表 A.4 疾病诊断和用药情况表、表 A.5 健康相关问题)
            LambdaQueryWrapper<AeaTaskEvaluateExecutionRecord> aeaTaskEvaluateExecutionRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
            for (AeaQuestionnaireVo questionnaireVo : questionnaireIdChildrenIds){
                aeaTaskEvaluateExecutionRecordLambdaQueryWrapper.eq(AeaTaskEvaluateExecutionRecord::getQuestionId, questionnaireVo.getId());
                aeaTaskEvaluateExecutionRecordVos = aeaTaskEvaluateExecutionRecordMapper.selectVoList(aeaTaskEvaluateExecutionRecordLambdaQueryWrapper);
                //TODO 填入到PDF数据
                log.info("查询到老年人能力评估基本信息表问卷问题所答信息: {}", aeaTaskEvaluateExecutionRecordVos);
            }

            /*--------------------------第三节PDF数据---------------------------------*/
            //根据问卷问题id获取->老年人能力评估基本信息表问卷问题所答信息(包含：表 B.1 老年人能力评估表、表B.2 基础运动能力评估表、表 B.3 精神状态评估表、表B.4 感知觉与社会参与评估表)
            //构建老年人能力评估questionnaireIdScoringChildrenId
            List<AeaQuestionnaireVo> questionnaireIdScoringChildrenId = new ArrayList<>();
            for (AeaQuestionnaireVo vo : aeaQuestionnaireVoList){
                if (vo.getTitle().equals("老年人能力评估")){
                    //根据id查询出子节点
                    aeaQuestionnaireLambdaQueryWrapper.eq(AeaQuestionnaire::getParentId, vo.getId());
                    questionnaireIdScoringChildrenId = aeaQuestionnaireMapper.selectVoList(aeaQuestionnaireLambdaQueryWrapper);
                }
            }
            //对questionnaireIdScoringChildrenId进行根据sort进行正序排序
            questionnaireIdScoringChildrenId = questionnaireIdScoringChildrenId.stream().sorted(Comparator.comparing(AeaQuestionnaireVo::getSort)).toList();
            //获取评估任务模块记录表信息
            LambdaQueryWrapper<AeaTaskEvaluateInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AeaTaskEvaluateInfo::getTaskId, aeaTaskEvaluateVo.getId());
            List<AeaTaskEvaluateInfoVo> aeaTaskEvaluateInfoVoList = aeaTaskEvaluateInfoMapper.selectVoList(queryWrapper);
            log.info("查询到评估任务模块记录表数量: {}", !aeaTaskEvaluateInfoVoList.isEmpty() ? aeaTaskEvaluateInfoVoList.size() : 0);
            //获取评估任务执行表中答题记录
            List<AeaTaskEvaluateExecutionRecordVo> recordVoList;
            for (AeaQuestionnaireVo aeaQuestionnaireVo : questionnaireIdScoringChildrenId){
                aeaTaskEvaluateExecutionRecordLambdaQueryWrapper.eq(AeaTaskEvaluateExecutionRecord::getQuestionId,aeaQuestionnaireVo.getId());
                recordVoList = aeaTaskEvaluateExecutionRecordMapper.selectVoList(aeaTaskEvaluateExecutionRecordLambdaQueryWrapper);
                //TODO 填入到PDF数据
                log.info("查询到老年人能力评估表中模块的答题记录: {}", recordVoList);
            }
            /*--------------------------第四节PDF数据---------------------------------*/
            // 1. 提交老年人能力评估报告
            AeaEvaluateReport aeaEvaluateReport = BeanUtil.copyProperties(aeaEvaluateReportBo, AeaEvaluateReport.class);
            // 设置报告编码
            aeaEvaluateReport.setCode(generateReportCode());
            // 设置创建时间
            aeaEvaluateReport.setCreateTime(new Date());
            aeaEvaluateReport.setUpdateTime(new Date());
            //新增报告记录前验证：自理能力得分、基础运动能力得分、精神状态得分、感知觉与社会参与得分、总分
            //获取老年人能力评估表中模块的得分情况
            LambdaQueryWrapper<AeaQuestionnaire> questionnaireVoLambdaQueryWrapper = new LambdaQueryWrapper<>();
            questionnaireVoLambdaQueryWrapper.eq(AeaQuestionnaire::getTitle, "老年人能力评估");
            List<AeaQuestionnaireVo>  questionnaireVoList  = aeaQuestionnaireMapper.selectVoList(questionnaireVoLambdaQueryWrapper);
            //对questionnaireVoList进行根据sort进行正序排序
            questionnaireVoList = questionnaireVoList.stream().sorted(Comparator.comparing(AeaQuestionnaireVo::getSort)).toList();
            //记录自理能力得分、基础运动能力得分、精神状态得分、感知觉与社会参与得分、总分
            Map<String,Object> map = new HashMap<>();
            // 获取自理能力得分、基础运动能力得分、精神状态得分、感知觉与社会参与得分用于计算总分
            Integer totalScore = 0;
            for (AeaQuestionnaireVo aeaQuestionnaireVo : questionnaireVoList){
                LambdaQueryWrapper<AeaTaskEvaluateInfo> aeaTaskEvaluateInfoLambdaQueryWrapper = new LambdaQueryWrapper<>();
                aeaTaskEvaluateInfoLambdaQueryWrapper.eq(AeaTaskEvaluateInfo::getQuestionnaireId, aeaQuestionnaireVo.getId());
                AeaTaskEvaluateInfoVo aeaTaskEvaluateInfoVo = aeaTaskEvaluateInfoMapper.selectVoById(aeaTaskEvaluateInfoLambdaQueryWrapper);
                map.put(aeaQuestionnaireVo.getTitle(),aeaTaskEvaluateInfoVo.getTotalScore());
                log.info("{}模块分值为：{}",aeaQuestionnaireVo.getTitle(), aeaTaskEvaluateInfoVo.getTotalScore());
                if (aeaQuestionnaireVo.getTitle().equals("自理能力评估")){
                    aeaEvaluateReportBo.setSelfScore(aeaTaskEvaluateInfoVo.getTotalScore());
                }
                if (aeaQuestionnaireVo.getTitle().equals("基础运动能力评估表")){
                    aeaEvaluateReportBo.setBaseScore(aeaTaskEvaluateInfoVo.getTotalScore());
                }
                if (aeaQuestionnaireVo.getTitle().equals("精神状态评估表")){
                    aeaEvaluateReportBo.setMentionScore(aeaTaskEvaluateInfoVo.getTotalScore());
                }
                if (aeaQuestionnaireVo.getTitle().equals("感知觉与社会参与")){
                    aeaEvaluateReportBo.setFeelScore(aeaTaskEvaluateInfoVo.getTotalScore());
                }
                //计算总分
                totalScore += aeaTaskEvaluateInfoVo.getTotalScore();

            }
            /*----------------------------------第五节------------------------------------*/
            //设置总分
            map.put("totalScore",totalScore);
            log.info("记录自理能力得分、基础运动能力得分、精神状态得分、感知觉与社会参与得分、总分:{}",  map);
            aeaEvaluateReportBo.setTotalScore(totalScore);
            // 插入报告记录
            int insertResult = aeaEvaluateReportMapper.insert(aeaEvaluateReport);
            if (insertResult <= 0) {
                throw new RuntimeException("保存评估报告失败");
            }
            log.info("评估报告保存成功，报告ID: {}, 报告编码: {}", aeaEvaluateReport.getId(), aeaEvaluateReport.getCode());
            /*-----------------------------生成PDF------------------------------------*/
            // 调用评估报告服务生成PDF
            String pdfUrl = evaluateReportService.generatePdfReport(aeaEvaluateReport.getId());
            
            if (pdfUrl == null || pdfUrl.trim().isEmpty()) {
                throw new RuntimeException("PDF报告生成失败，返回的URL为空");
            }
            
            log.info("PDF报告生成成功，报告ID: {}, PDF链接: {}", aeaEvaluateReport.getId(), pdfUrl);
            
            // 4. 返回PDF报告的下载链接
            return pdfUrl;
            
        } catch (Exception e) {
            log.error("第五步生成PDF报告失败，任务ID: {}", aeaEvaluateReportBo.getTaskId(), e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据问卷问题id获取老年人能力评估表问卷问题所答信息
     */
    public void getQuestionnaireQuestionAnswers(List<AeaQuestionnaireVo> aeaQuestionnaireVoList,String name) {
        //获取老年人能力评估基本信息表->评估对象基本信息表所需填充数据
        LambdaQueryWrapper<AeaQuestionnaire> aeaQuestionnaireLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //构建老年人能力评估基本信息表questionnaireIdChildrenIds
        List<AeaQuestionnaireVo> questionnaireIdChildrenIds = new ArrayList<>();
        for (AeaQuestionnaireVo vo : aeaQuestionnaireVoList){
            if (vo.getTitle().equals(name)){
                //根据id查询出子节点
                aeaQuestionnaireLambdaQueryWrapper.eq(AeaQuestionnaire::getParentId, vo.getId());
                questionnaireIdChildrenIds = aeaQuestionnaireMapper.selectVoList(aeaQuestionnaireLambdaQueryWrapper);
            }
        }
        //对questionnaireIdChildrenIds进行根据sort进行正序排序
        questionnaireIdChildrenIds = questionnaireIdChildrenIds.stream().sorted(Comparator.comparing(AeaQuestionnaireVo::getSort)).toList();
        //获取老年人能力评估基本信息表Children列表
        List<AeaTaskEvaluateExecutionRecordVo> aeaTaskEvaluateExecutionRecordVos;
        //根据问卷问题id获取->问卷问题所答信息
        LambdaQueryWrapper<AeaTaskEvaluateExecutionRecord> aeaTaskEvaluateExecutionRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        for (AeaQuestionnaireVo questionnaireVo : questionnaireIdChildrenIds){
            aeaTaskEvaluateExecutionRecordLambdaQueryWrapper.eq(AeaTaskEvaluateExecutionRecord::getQuestionId, questionnaireVo.getId());
            aeaTaskEvaluateExecutionRecordVos = aeaTaskEvaluateExecutionRecordMapper.selectVoList(aeaTaskEvaluateExecutionRecordLambdaQueryWrapper);
            log.info("问卷问题所答信息: {}", aeaTaskEvaluateExecutionRecordVos);
            //TODO: 2023/6/5 PDF填充数据
        }
    }

    /**
     * 生成评估报告编号，格式为：AE + 当前日期时间 + 8位递增序列号
     * 示例：AE20250605143000000001
     */
    private String generateReportCode() {
        // 获取当前日期时间，格式为：yyyyMMddHHmmss
        String dateTime = DateUtils.dateTimeNow(FormatsType.YYYYMMDDHHMMSS);

        // 获取当日已生成的报告数量，用于生成递增序列号
        Long count = aeaEvaluateReportMapper.selectCount(new QueryWrapper<AeaEvaluateReport>()
                .like("code", "AE" + dateTime));

        // 计算下一个序列号，初始值为00000001
        long sequence = count == null ? 1 : count + 1;

        // 拼接评估报告编号
        return "AE" + dateTime + String.format("%08d", sequence);
    }

    /**
     * 获取问卷结构
     */
    @Override
    public AeaQuestionnaireVo getQuestionnaireStructure(Long questionnaireId) {
        try {
            AeaQuestionnaireVo questionnaire = questionnaireService.queryById(questionnaireId);
            if (questionnaire == null) {
                throw new RuntimeException("问卷不存在");
            }

            log.info("获取问卷结构成功，问卷ID: {}, 问卷名称: {}", questionnaireId, questionnaire.getTitle());
            return questionnaire;
        } catch (Exception e) {
            log.error("获取问卷结构失败，问卷ID: {}", questionnaireId, e);
            throw new RuntimeException("获取问卷结构失败: " + e.getMessage());
        }
    }

    /**
     * 获取问卷问题列表
     */
    @Override
    public List<AeaQuestionnaireQuestionVo> getQuestionnaireQuestions(Long questionnaireId) {
        try {
            AeaQuestionnaireQuestionBo queryBo = new AeaQuestionnaireQuestionBo();
            queryBo.setQuestionnaireId(questionnaireId);

            List<AeaQuestionnaireQuestionVo> questions = questionnaireQuestionService.queryList(queryBo);

            if (CollUtil.isEmpty(questions)) {
                log.warn("问卷没有问题，问卷ID: {}", questionnaireId);
                throw new RuntimeException("问卷没有配置问题");
            }

            log.info("获取问卷问题成功，问卷ID: {}, 问题数量: {}", questionnaireId, questions.size());
            return questions;
        } catch (Exception e) {
            log.error("获取问卷问题失败，问卷ID: {}", questionnaireId, e);
            throw new RuntimeException("获取问卷问题失败: " + e.getMessage());
        }
    }

    /**
     * 提交答案并生成报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitAnswersAndGenerateReport(BatchAnswerSubmitBo batchSubmitBo) {
        try {
            Long taskId = batchSubmitBo.getTaskId();
            Long infoId = batchSubmitBo.getInfoId();

            log.info("开始提交答案，任务ID: {}, 详情ID: {}, 答案数量: {}",
                    taskId, infoId, batchSubmitBo.getRecords().size());

            // 1. 批量保存答题记录
            Boolean saveResult = executionRecordService.batchSaveAnswerRecords(batchSubmitBo.getRecords());
            if (!saveResult) {
                throw new RuntimeException("保存答题记录失败");
            }
            log.info("批量保存答题记录成功");
            
            // 2. 计算总分
            Integer totalScore = executionRecordService.calculateTotalScore(taskId, infoId);
            log.info("计算总分完成，总分: {}", totalScore);
            
            // 3. 完成评估详情（更新总分和状态）
            evaluateInfoService.completeEvaluateInfo(infoId, totalScore);
            log.info("完成评估详情成功");
            
            // 4. 完成评估任务
            taskService.completeTask(taskId);
            log.info("完成评估任务成功");
            
            // 5. 创建评估报告
            AeaEvaluateReportBo reportBo = buildEvaluateReportBo(taskId, infoId);
            Long reportId = evaluateReportService.createEvaluateReport(reportBo);
            log.info("创建评估报告成功，报告ID: {}", reportId);
            
            // 6. 生成PDF报告
            String pdfUrl = evaluateReportService.generatePdfReport(reportId);
            log.info("生成PDF报告成功，报告URL: {}", pdfUrl);
            
            return reportId;
        } catch (Exception e) {
            log.error("提交答案并生成报告失败，任务ID: {}, 详情ID: {}",
                    batchSubmitBo.getTaskId(), batchSubmitBo.getInfoId(), e);
            throw new RuntimeException("提交答案并生成报告失败: " + e.getMessage());
        }
    }

    /**
     * 获取报告下载链接
     */
    @Override
    public String getReportDownloadUrl(Long taskId) {
        try {
            // 根据任务ID查询报告
            AeaEvaluateReportVo report = evaluateReportService.queryByTaskId(taskId);
            if (report == null) {
                throw new RuntimeException("评估报告不存在");
            }

            // 获取下载链接
            String downloadUrl = evaluateReportService.getReportDownloadUrl(report.getId());
            log.info("获取报告下载链接成功，任务ID: {}, 报告ID: {}, 下载链接: {}",
                    taskId, report.getId(), downloadUrl);

            return downloadUrl;
        } catch (Exception e) {
            log.error("获取报告下载链接失败，任务ID: {}", taskId, e);
            throw new RuntimeException("获取报告下载链接失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成PDF报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String regeneratePdfReport(Long taskId) {
        try {
            // 根据任务ID查询报告
            AeaEvaluateReportVo report = evaluateReportService.queryByTaskId(taskId);
            if (report == null) {
                throw new RuntimeException("评估报告不存在");
            }

            // 重新生成PDF报告
            String pdfUrl = evaluateReportService.generatePdfReport(report.getId());
            log.info("重新生成PDF报告成功，任务ID: {}, 报告ID: {}, PDF链接: {}",
                    taskId, report.getId(), pdfUrl);

            return pdfUrl;
        } catch (Exception e) {
            log.error("重新生成PDF报告失败，任务ID: {}", taskId, e);
            throw new RuntimeException("重新生成PDF报告失败: " + e.getMessage());
        }
    }

    /**
     * 构建评估报告BO对象
     */
    private AeaEvaluateReportBo buildEvaluateReportBo(Long taskId, Long infoId) {
        // 获取任务信息
        AeaTaskEvaluateVo task = taskService.queryById(taskId);
        if (task == null) {
            throw new RuntimeException("任务不存在");
        }

        // 获取评估详情
        AeaTaskEvaluateInfoVo evaluateInfo = evaluateInfoService.queryById(infoId);
        if (evaluateInfo == null) {
            throw new RuntimeException("评估详情不存在");
        }

        // 构建报告BO
        AeaEvaluateReportBo reportBo = new AeaEvaluateReportBo();
        reportBo.setTaskId(taskId);
        reportBo.setTotalScore(evaluateInfo.getTotalScore());
        reportBo.setDelFlag(0);

        // 可以根据需要设置其他字段
        // reportBo.setLocation("评估地点");
        // reportBo.setReasonCode("评估原因代码");

        return reportBo;
    }
}