package org.dromara.cyly.aea.refactor.service;

import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReport;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateInfo;
import org.dromara.cyly.aea.refactor.domain.bo.ScoreResultBo;

import java.util.List;
import java.util.Map;

/**
 * 分数计算服务接口
 */
public interface IScoreCalculationService {

    /**
     * 根据评估信息ID计算各项得分和总分及等级
     *
     * @param evaluateInfoId 评估任务详情ID
     * @param report         待更新分数的报告对象
     * @return 包含各维度得分、总分和等级的报告对象
     */
    AeaEvaluateReport calculateScoresAndLevel(Long evaluateInfoId, AeaEvaluateReport report);

    /**
     * (辅助方法，如果需要在外部单独获取原始分数映射时使用)
     * 根据评估信息ID计算各分类的原始得分
     *
     * @param evaluateInfoId 评估任务详情ID
     * @return Key为问题分类（如 B.1, B.2等），Value为该分类总得分
     */
    Map<String, Integer> calculateCategoryScores(Long evaluateInfoId);

    /**
     * 根据总分计算初步评估等级
     *
     * @param totalScore 总分
     * @return 初步评估等级 (1-能力完好, 2-轻度受损, 3-中度受损, 4-重度受损, 5-完全丧失)
     */
    Integer calculateFirstLevel(Integer totalScore);

    /**
     * (如果需要更复杂的等级调整逻辑，可以扩展此接口)
     * 根据初步等级和调整依据计算最终评估等级
     *
     * @param firstLevel      初步评估等级
     * @param adjustmentBasis 调整依据
     * @return 最终评估等级
     */
    Integer calculateFinalLevel(Integer firstLevel, String adjustmentBasis);

}