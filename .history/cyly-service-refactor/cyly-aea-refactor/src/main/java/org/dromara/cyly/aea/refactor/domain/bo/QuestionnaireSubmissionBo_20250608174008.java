package org.dromara.cyly.aea.refactor.domain.bo;

import lombok.Data;
import java.util.List;

/**
 * 问卷提交业务对象
 */
@Data
public class QuestionnaireSubmissionBo {

    /**
     * 长者ID
     */
    private Long elderId;

    /**
     * 问卷ID
     */
    private Long questionnaireId;

    /**
     * 主评估员ID
     */
    private Long assessorId;

    /**
     * 评估地点
     */
    private String location;

    /**
     * 答案列表
     */
    private List<AnswerBo> answers;

    /**
     * 主评估员签名图片OSS地址
     */
    private String assessorSign;

    /**
     * 副评估员签名图片OSS地址
     */
    private String deputySign;

    /**
     * 信息提供者签名图片OSS地址
     */
    private String familySign; // 在文档中是 familySign，对应报告生成是 informationProviderSign

    /**
     * 内部答案业务对象
     */
    @Data
    public static class AnswerBo {
        /**
         * 问题ID
         */
        private Long questionId;

        /**
         * 答案ID (选择题时)
         */
        private Long answerId;

        /**
         * 用户输入的文本内容 (非选项答案时填写)
         */
        private String content;

        /**
         * 评分值 (如果前端可以计算或预定义)
         */
        private Integer score;
    }
}