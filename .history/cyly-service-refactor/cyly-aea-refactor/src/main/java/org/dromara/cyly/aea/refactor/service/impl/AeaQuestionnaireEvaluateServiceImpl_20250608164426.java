package org.dromara.cyly.aea.refactor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.AbilityLevelEnum;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.enums.SourceEnum;
import org.dromara.common.core.enums.TaskStatusEnum;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.cyly.aea.refactor.domain.*;
import org.dromara.cyly.aea.refactor.domain.bo.*;
import org.dromara.cyly.aea.refactor.domain.vo.*;
import org.dromara.cyly.aea.refactor.mapper.*;
import org.dromara.cyly.aea.refactor.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

/**
 * 问卷评估核心业务服务实现
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AeaQuestionnaireEvaluateServiceImpl implements IAeaQuestionnaireEvaluateService {

    private final IAeaTaskEvaluateService taskService;
    private final IAeaTaskEvaluateInfoService evaluateInfoService;
    private final IAeaTaskEvaluateExecutionRecordService executionRecordService;
    private final IAeaEvaluateReportService evaluateReportService;
    private final IAeaQuestionnaireService questionnaireService;
    private final IAeaQuestionnaireQuestionService questionnaireQuestionService;
    private final IAeaQuestionnaireAnswerService aeaQuestionnaireAnswerService;
    private final AeaQuestionnaireAnswerMapper aeaQuestionnaireAnswerMapper;
    private final AeaTaskEvaluateExecutionRecordMapper aeaTaskEvaluateExecutionRecordMapper;
    private final AeaTaskEvaluateInfoMapper aeaTaskEvaluateInfoMapper;
    private final AeaEvaluateReportMapper aeaEvaluateReportMapper;
    private final AeaElderHealthInfoMapper aeaElderHealthInfoMapper;
    private final AeaQuestionnaireMapper aeaQuestionnaireMapper;
    private final AeaTaskEvaluateMapper aeaTaskEvaluateMapper;


    /**
     * 开始评估，先查询是否有长者的建档信息
     */
    @Override
    public Boolean startEvaluate(Long taskId) {
        //  1. 查询长者的建档信息
        AeaTaskEvaluateVo task = taskService.queryById(taskId);
        // 2. 如果没有长者的建档信息，返回false
        Long rows = aeaElderHealthInfoMapper.selectCount(new QueryWrapper<AeaElderHealthInfo>().eq("aea_elder_id", task.getElderId()));
        if (rows == 0) {
            log.error("未查找到:{}的建档信息", task.getElderId());
            return false;
        }
        // 3. 如果有长者的建档信息，更新任务状态为进行中
        AeaTaskEvaluateBo taskBo = new AeaTaskEvaluateBo();
        taskBo.setId(task.getId());
        taskBo.setStartTime(new Date());
        taskBo.setStatus(TaskStatusEnum.IN_PROGRESS.getCode());
        taskBo.setUpdateTime(new Date());
        //  4. 更新任务
        return taskService.updateByBo(taskBo);
    }

    /**
     * 第一步：创建评估任务流程
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createEvaluateTaskFlow(AeaTaskEvaluateBo aeaTaskEvaluate) {
        try {
            // 1. 创建评估任务
            aeaTaskEvaluate.setSource(SourceEnum.ASSESSOR.getCode());
            aeaTaskEvaluate.setAssignTime(DateUtils.getNowDate());
            aeaTaskEvaluate.setTaskNum(generateReportCode()); // 任务编号
            Long taskId = taskService.createEvaluateTask(aeaTaskEvaluate);
            log.info("创建评估任务成功，任务ID: {}", taskId);

            // 2. 查询评估问卷表，设置评估详情的任务ID
            AeaQuestionnaireBo aeaQuestionnaireBo = new AeaQuestionnaireBo();
            aeaQuestionnaireBo.setType(2);
            List<AeaQuestionnaireVo> questionnaireVoList = questionnaireService.queryList(aeaQuestionnaireBo);
            // 加入需要答题的问卷
            questionnaireVoList.forEach(questionnaireVo -> {
                AeaTaskEvaluateInfoBo evaluateInfoBo = new AeaTaskEvaluateInfoBo();
                evaluateInfoBo.setTaskId(taskId);
                evaluateInfoBo.setQuestionnaireId(questionnaireVo.getId());
                evaluateInfoBo.setStartTime(new Date());
                // 3. 创建评估详情
                Long infoId = evaluateInfoService.createEvaluateInfo(evaluateInfoBo);
                log.info("创建评估详情成功，详情ID: {}", infoId);
            });
            return taskId;
        } catch (Exception e) {
            log.error("创建评估任务流程失败", e);
            throw new RuntimeException("创建评估任务失败，请重新创建！");
        }
    }

    /**
     * 第二步：获取问卷目录结构
     * @param taskId 任务ID
     * @return 任务详情列表
     */
    @Override
    public List<AeaTaskEvaluateInfoVo> getAeaTaskEvaluateInfoList(Long taskId) {
        List<AeaTaskEvaluateInfoVo> infoVoList = new ArrayList<>();
        try {
            AeaTaskEvaluateInfoBo queryBo = new AeaTaskEvaluateInfoBo();
            queryBo.setTaskId(taskId);
            infoVoList = evaluateInfoService.queryList(queryBo);
            log.info("获取任务详情列表成功 · 任务ID: {}, 详情数量: {}", taskId, infoVoList.size());
        } catch (Exception e) {
            log.error("{}:问卷目录不存在！", taskId);
        }
        return infoVoList;
    }

    /**
     * 第三步：提交答案
     */
    @Override
    public Boolean submitAnswers(AeaTaskSubmitAnswerBo bo) {

        List<Long> answerIdList = new ArrayList<>();
        Long infoId = bo.getInfoId();
        Long taskId = bo.getTaskId();
        Long questionnaireId = bo.getQuestionnaireId(); // 问卷id
        List<AeaTaskEvaluateExecutionRecordBo> evaluateExecutionRecordBos = bo.getEvaluateExecutionRecordBos(); // 答卷内容
        log.info("【开始提交答案 · 任务ID: {}，详情ID: {}，问卷ID: {}，问题数量: {}】", taskId, infoId, questionnaireId, evaluateExecutionRecordBos.size());
        // 1. 查询模块目录下的题库数量
        AeaQuestionnaireQuestionBo questionnaireQuestionBo = new AeaQuestionnaireQuestionBo();
        questionnaireQuestionBo.setQuestionnaireId(questionnaireId);
        List<AeaQuestionnaireQuestionVo> aeaQuestionnaireQuestionVos = questionnaireQuestionService.queryList(questionnaireQuestionBo);
        // 2. 提交的答题数量与题库数量进行比对
        if (aeaQuestionnaireQuestionVos.size() != evaluateExecutionRecordBos.size()) {
            log.error("提交失败：提交答案为: {}, 题库数量为: {}", evaluateExecutionRecordBos.size(), aeaQuestionnaireQuestionVos.size());
            throw new RuntimeException("提交失败：请检查是否全部答完！");
        }
        evaluateExecutionRecordBos.forEach(record -> {
            answerIdList.add(record.getAnswerId());
        });
        List<AeaQuestionnaireAnswerVo> answerVos = aeaQuestionnaireAnswerMapper.selectVoByIds(answerIdList);
        // 3. 批量保存答题记录
        List<AeaTaskEvaluateExecutionRecord> evaluateExecutionRecordList = BeanUtil.copyToList(evaluateExecutionRecordBos, AeaTaskEvaluateExecutionRecord.class);
        // 得分初始化
        int score = 0;
        for (AeaQuestionnaireAnswerVo answerVo : answerVos) {
            // 得分累加
            score += answerVo.getScore();
        }
        log.info("【提交答案成功 · 任务ID: {}，详情ID: {}，问卷ID: {}，分数: {}】", taskId, infoId, questionnaireId, score);
        // 批量保存
        aeaTaskEvaluateExecutionRecordMapper.insertBatch(evaluateExecutionRecordList);
        // 更新分数，结束时间，评估状态
        AeaTaskEvaluateInfoBo aeaTaskEvaluateInfoBo = new AeaTaskEvaluateInfoBo();
        aeaTaskEvaluateInfoBo.setId(infoId);
        aeaTaskEvaluateInfoBo.setTotalScore(score);
        aeaTaskEvaluateInfoBo.setEndTime(new Date());
        aeaTaskEvaluateInfoBo.setStatus(TaskStatusEnum.COMPLETED.getCode());
        return evaluateInfoService.updateByBo(aeaTaskEvaluateInfoBo);
    }

    /**
     * 第四步：获取答题结果
     */
    @Override
    public Map<String, Object> getEvaluateReport(Long taskId, Long questionnaireId) {
        // 1. 获取答题结果
        LambdaQueryWrapper<AeaTaskEvaluateInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(taskId != null, AeaTaskEvaluateInfo::getTaskId, taskId);
        queryWrapper.gt(AeaTaskEvaluateInfo::getTotalScore, (0));
        List<AeaTaskEvaluateInfoVo> info = aeaTaskEvaluateInfoMapper.selectVoList(queryWrapper);
        log.info("【获取答题结果成功 · 任务ID: {}，问卷ID: {}，结果数量: {}】", taskId, questionnaireId, info.size());
        if (info.isEmpty()) {
            log.error("【获取答题结果失败 · 任务ID: {}，问卷ID: {}】", taskId, questionnaireId);
            throw new RuntimeException("获取答题结果失败");
        }
        int total = 0;
        for (AeaTaskEvaluateInfoVo infoVo : info) {
            total += infoVo.getTotalScore();
        }
        Map<String, Object> result = new HashMap<>();
        // 获取模块列表详细
        result.put("records", info);
        // 总分
        result.put("total", total);
        if (total >= 90) {
            result.put("CapabilityLevel", AbilityLevelEnum.INTACT.getDescription());
        }
        if (total > 66 && total < 90) {
            result.put("CapabilityLevel", AbilityLevelEnum.MILD_DISABILITY.getDescription());
        }
        if (total > 46 && total <= 66) {
            result.put("CapabilityLevel", AbilityLevelEnum.MODERATE_DISABILITY.getDescription());
        }
        if (total > 30 && total <= 46) {
            result.put("CapabilityLevel", AbilityLevelEnum.SEVERE_DISABILITY.getDescription());
        }
        if (total <= 30) {
            result.put("CapabilityLevel", AbilityLevelEnum.FULL_DISABILITY.getDescription());
        }
        return result;
    }

    /**
     * 第五步：生成PDF报告
     * 完整实现PDF报告生成流程
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateReportPdf(Long taskId) {
        log.info("开始生成PDF报告，任务ID: {}", taskId);
        
        try {
            // 1. 获取问卷信息
            AeaQuestionnaireVo questionnaire = getQuestionnaireByTaskId(taskId);
            if (questionnaire == null) {
                throw new RuntimeException("未找到对应的问卷信息");
            }
            
            // 2. 获取任务评估信息
             AeaTaskEvaluateVo taskEvaluate = taskService.queryById(taskId);
             if (taskEvaluate == null) {
                 throw new RuntimeException("未找到对应的任务评估信息");
             }
            
            // 3. 获取评估详情信息（通过taskId获取infoId）
            LambdaQueryWrapper<AeaTaskEvaluateInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AeaTaskEvaluateInfo::getTaskId, taskId);
            List<AeaTaskEvaluateInfoVo> evaluateInfoList = aeaTaskEvaluateInfoMapper.selectVoList(queryWrapper);
            if (evaluateInfoList.isEmpty()) {
                throw new RuntimeException("未找到对应的评估详情信息");
            }
            
            // 取第一个评估详情信息（通常一个任务对应一个评估详情）
            AeaTaskEvaluateInfoVo evaluateInfo = evaluateInfoList.get(0);
            Long infoId = evaluateInfo.getId();
            
            // 4. 获取答题记录（使用正确的infoId）
            LambdaQueryWrapper<AeaTaskEvaluateExecutionRecord> recordQueryWrapper = new LambdaQueryWrapper<>();
            recordQueryWrapper.eq(AeaTaskEvaluateExecutionRecord::getTaskId, taskId);
            recordQueryWrapper.eq(AeaTaskEvaluateExecutionRecord::getInfoId, infoId);
            List<AeaTaskEvaluateExecutionRecordVo> executionRecords = 
                aeaTaskEvaluateExecutionRecordMapper.selectVoList(recordQueryWrapper);
            
            // 5. 获取问卷题目和答案选项详细信息
            Map<Long, AeaQuestionnaireQuestionVo> questionMap = new HashMap<>();
            Map<Long, List<AeaQuestionnaireAnswerVo>> answerMap = new HashMap<>();
            
            // 查询问卷的所有题目
             AeaQuestionnaireQuestionBo questionBo = new AeaQuestionnaireQuestionBo();
             questionBo.setQuestionnaireId(questionnaire.getId());
             List<AeaQuestionnaireQuestionVo> questions = questionnaireQuestionService.queryList(questionBo);
             
             for (AeaQuestionnaireQuestionVo question : questions) {
                 questionMap.put(question.getId(), question);
                 
                 // 查询每个题目的答案选项
                 AeaQuestionnaireAnswerBo answerBo = new AeaQuestionnaireAnswerBo();
                 answerBo.setQuestionId(question.getId());
                 List<AeaQuestionnaireAnswerVo> answers = aeaQuestionnaireAnswerService.queryList(answerBo);
                 answerMap.put(question.getId(), answers);
             }
            
            // 6. 获取被评估人（长者）信息
            AeaElderHealthInfoVo elderInfo = null;
            if (taskEvaluate.getElderId() != null) {
                elderInfo = aeaElderHealthInfoMapper.selectVoById(taskEvaluate.getElderId());
            }
            
            // 7. 获取机构信息（从长者信息或任务信息中获取）
            String institutionName = "康养机构"; // 默认机构名称
            if (elderInfo != null && elderInfo.getInstitutionId() != null) {
                // 这里可以根据机构ID查询机构详细信息
                // AeaInstitutionVo institution = institutionService.getInfo(elderInfo.getInstitutionId());
                // institutionName = institution.getName();
            }
            
            // 8. 计算各项分数
            Integer selfScore = calculateSelfScore(executionRecords);
            Integer baseScore = calculateBaseScore(executionRecords);
            Integer mentionScore = calculateMentionScore(executionRecords);
            Integer feelScore = calculateFeelScore(executionRecords);
            Integer totalScore = selfScore + baseScore + mentionScore + feelScore;
            
            // 9. 构建评估报告对象
            AeaEvaluateReportBo aeaEvaluateReportBo = buildEvaluateReportBo(taskId, infoId);
            aeaEvaluateReportBo.setSelfScore(selfScore);
            aeaEvaluateReportBo.setBaseScore(baseScore);
            aeaEvaluateReportBo.setMentionScore(mentionScore);
            aeaEvaluateReportBo.setFeelScore(feelScore);
            aeaEvaluateReportBo.setTotalScore(totalScore);
            
            // 设置其他必要的报告信息
            aeaEvaluateReportBo.setLocation("评估地点"); // 可以从配置或参数中获取
            if (taskEvaluate.getAssessorUserId() != null) {
                aeaEvaluateReportBo.setAssessorEvaluateUserId(taskEvaluate.getAssessorUserId());
            }
            if (taskEvaluate.getDeputyUserId() != null) {
                aeaEvaluateReportBo.setDeputyEvaluateUserId(taskEvaluate.getDeputyUserId());
            }
            
            // 10. 保存评估报告记录
            AeaEvaluateReport aeaEvaluateReport = BeanUtil.copyProperties(aeaEvaluateReportBo, AeaEvaluateReport.class);
            aeaEvaluateReport.setCode(generateReportCode());
            aeaEvaluateReport.setCreateTime(new Date());
            aeaEvaluateReport.setUpdateTime(new Date());
            
            int insertResult = aeaEvaluateReportMapper.insert(aeaEvaluateReport);
            if (insertResult <= 0) {
                throw new RuntimeException("保存评估报告失败");
            }
            
            // 11. 生成PDF报告（传递完整的数据信息）
            String pdfUrl = generatePdfReportWithData(aeaEvaluateReport.getId(), 
                questionnaire, taskEvaluate, evaluateInfo, elderInfo, 
                executionRecords, questionMap, answerMap, institutionName);
            
            log.info("PDF报告生成成功，任务ID: {}, PDF地址: {}", taskId, pdfUrl);
            return pdfUrl;
            
        } catch (Exception e) {
            log.error("生成PDF报告失败，任务ID: {}", taskId, e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据问卷问题id获取老年人能力评估表问卷问题所答信息
     */
    public void getQuestionnaireQuestionAnswers(List<AeaQuestionnaireVo> aeaQuestionnaireVoList,String name) {
        //获取老年人能力评估基本信息表->评估对象基本信息表所需填充数据
        LambdaQueryWrapper<AeaQuestionnaire> aeaQuestionnaireLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //构建老年人能力评估基本信息表questionnaireIdChildrenIds
        List<AeaQuestionnaireVo> questionnaireIdChildrenIds = new ArrayList<>();
        for (AeaQuestionnaireVo vo : aeaQuestionnaireVoList){
            if (vo.getTitle().equals(name)){
                //根据id查询出子节点
                aeaQuestionnaireLambdaQueryWrapper.eq(AeaQuestionnaire::getParentId, vo.getId());
                questionnaireIdChildrenIds = aeaQuestionnaireMapper.selectVoList(aeaQuestionnaireLambdaQueryWrapper);
            }
        }
        //对questionnaireIdChildrenIds进行根据sort进行正序排序
        questionnaireIdChildrenIds = questionnaireIdChildrenIds.stream().sorted(Comparator.comparing(AeaQuestionnaireVo::getSort)).toList();
        //获取老年人能力评估基本信息表Children列表
        List<AeaTaskEvaluateExecutionRecordVo> aeaTaskEvaluateExecutionRecordVos;
        //根据问卷问题id获取->问卷问题所答信息
        LambdaQueryWrapper<AeaTaskEvaluateExecutionRecord> aeaTaskEvaluateExecutionRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        for (AeaQuestionnaireVo questionnaireVo : questionnaireIdChildrenIds){
            aeaTaskEvaluateExecutionRecordLambdaQueryWrapper.eq(AeaTaskEvaluateExecutionRecord::getQuestionId, questionnaireVo.getId());
            aeaTaskEvaluateExecutionRecordVos = aeaTaskEvaluateExecutionRecordMapper.selectVoList(aeaTaskEvaluateExecutionRecordLambdaQueryWrapper);
            log.info("问卷问题所答信息: {}", aeaTaskEvaluateExecutionRecordVos);
            //TODO: 2023/6/5 PDF填充数据
        }
    }

    /**
     * 生成评估报告编号，格式为：AE + 当前日期时间 + 8位递增序列号
     * 示例：AE20250605143000000001
     */
    private String generateReportCode() {
        // 获取当前日期时间，格式为：yyyyMMddHHmmss
        String dateTime = DateUtils.dateTimeNow(FormatsType.YYYYMMDDHHMMSS);

        // 获取当日已生成的报告数量，用于生成递增序列号
        Long count = aeaEvaluateReportMapper.selectCount(new QueryWrapper<AeaEvaluateReport>()
                .like("code", "AE" + dateTime));

        // 计算下一个序列号，初始值为00000001
        long sequence = count == null ? 1 : count + 1;

        // 拼接评估报告编号
        return "AE" + dateTime + String.format("%08d", sequence);
    }

    /**
     * 获取问卷结构
     */
    @Override
    public AeaQuestionnaireVo getQuestionnaireStructure(Long questionnaireId) {
        try {
            AeaQuestionnaireVo questionnaire = questionnaireService.queryById(questionnaireId);
            if (questionnaire == null) {
                throw new RuntimeException("问卷不存在");
            }

            log.info("获取问卷结构成功，问卷ID: {}, 问卷名称: {}", questionnaireId, questionnaire.getTitle());
            return questionnaire;
        } catch (Exception e) {
            log.error("获取问卷结构失败，问卷ID: {}", questionnaireId, e);
            throw new RuntimeException("获取问卷结构失败: " + e.getMessage());
        }
    }

    /**
     * 获取问卷问题列表
     */
    @Override
    public List<AeaQuestionnaireQuestionVo> getQuestionnaireQuestions(Long questionnaireId) {
        try {
            AeaQuestionnaireQuestionBo queryBo = new AeaQuestionnaireQuestionBo();
            queryBo.setQuestionnaireId(questionnaireId);

            List<AeaQuestionnaireQuestionVo> questions = questionnaireQuestionService.queryList(queryBo);

            if (CollUtil.isEmpty(questions)) {
                log.warn("问卷没有问题，问卷ID: {}", questionnaireId);
                throw new RuntimeException("问卷没有配置问题");
            }

            log.info("获取问卷问题成功，问卷ID: {}, 问题数量: {}", questionnaireId, questions.size());
            return questions;
        } catch (Exception e) {
            log.error("获取问卷问题失败，问卷ID: {}", questionnaireId, e);
            throw new RuntimeException("获取问卷问题失败: " + e.getMessage());
        }
    }

    /**
     * 提交答案并生成报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long submitAnswersAndGenerateReport(BatchAnswerSubmitBo batchSubmitBo) {
        try {
            Long taskId = batchSubmitBo.getTaskId();
            Long infoId = batchSubmitBo.getInfoId();

            log.info("开始提交答案，任务ID: {}, 详情ID: {}, 答案数量: {}",
                    taskId, infoId, batchSubmitBo.getRecords().size());

            // 1. 批量保存答题记录
            Boolean saveResult = executionRecordService.batchSaveAnswerRecords(batchSubmitBo.getRecords());
            if (!saveResult) {
                throw new RuntimeException("保存答题记录失败");
            }
            log.info("批量保存答题记录成功");
            
            // 2. 计算总分
            Integer totalScore = executionRecordService.calculateTotalScore(taskId, infoId);
            log.info("计算总分完成，总分: {}", totalScore);
            
            // 3. 完成评估详情（更新总分和状态）
            evaluateInfoService.completeEvaluateInfo(infoId, totalScore);
            log.info("完成评估详情成功");
            
            // 4. 完成评估任务
            taskService.completeTask(taskId);
            log.info("完成评估任务成功");
            
            // 5. 创建评估报告
            AeaEvaluateReportBo reportBo = buildEvaluateReportBo(taskId, infoId);
            Long reportId = evaluateReportService.createEvaluateReport(reportBo);
            log.info("创建评估报告成功，报告ID: {}", reportId);
            
            // 6. 生成PDF报告
            String pdfUrl = evaluateReportService.generatePdfReport(reportId);
            log.info("生成PDF报告成功，报告URL: {}", pdfUrl);
            
            return reportId;
        } catch (Exception e) {
            log.error("提交答案并生成报告失败，任务ID: {}, 详情ID: {}",
                    batchSubmitBo.getTaskId(), batchSubmitBo.getInfoId(), e);
            throw new RuntimeException("提交答案并生成报告失败: " + e.getMessage());
        }
    }

    /**
     * 获取报告下载链接
     */
    @Override
    public String getReportDownloadUrl(Long taskId) {
        try {
            // 根据任务ID查询报告
            AeaEvaluateReportVo report = evaluateReportService.queryByTaskId(taskId);
            if (report == null) {
                throw new RuntimeException("评估报告不存在");
            }

            // 获取下载链接
            String downloadUrl = evaluateReportService.getReportDownloadUrl(report.getId());
            log.info("获取报告下载链接成功，任务ID: {}, 报告ID: {}, 下载链接: {}",
                    taskId, report.getId(), downloadUrl);

            return downloadUrl;
        } catch (Exception e) {
            log.error("获取报告下载链接失败，任务ID: {}", taskId, e);
            throw new RuntimeException("获取报告下载链接失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成PDF报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String regeneratePdfReport(Long taskId) {
        try {
            // 根据任务ID查询报告
            AeaEvaluateReportVo report = evaluateReportService.queryByTaskId(taskId);
            if (report == null) {
                throw new RuntimeException("评估报告不存在");
            }

            // 重新生成PDF报告
            String pdfUrl = evaluateReportService.generatePdfReport(report.getId());
            log.info("重新生成PDF报告成功，任务ID: {}, 报告ID: {}, PDF链接: {}",
                    taskId, report.getId(), pdfUrl);

            return pdfUrl;
        } catch (Exception e) {
            log.error("重新生成PDF报告失败，任务ID: {}", taskId, e);
            throw new RuntimeException("重新生成PDF报告失败: " + e.getMessage());
        }
    }

    /**
     * 构建评估报告BO对象
     */
    private AeaEvaluateReportBo buildEvaluateReportBo(Long taskId, Long infoId) {
        AeaEvaluateReportBo bo = new AeaEvaluateReportBo();
        bo.setTaskId(taskId);
        bo.setInfoId(infoId);
        bo.setCode(generateReportCode());
        bo.setCreateTime(new Date());
        return bo;
    }
    
    /**
     * 生成包含完整数据的PDF报告
     */
    private String generatePdfReportWithData(Long reportId, 
                                           AeaQuestionnaireVo questionnaire,
                                           AeaTaskEvaluateVo taskEvaluate,
                                           AeaTaskEvaluateInfoVo evaluateInfo,
                                           AeaElderHealthInfoVo elderInfo,
                                           List<AeaTaskEvaluateExecutionRecordVo> executionRecords,
                                           Map<Long, AeaQuestionnaireQuestionVo> questionMap,
                                           Map<Long, List<AeaQuestionnaireAnswerVo>> answerMap,
                                           String institutionName) {
        try {
            // 获取报告基本信息
            AeaEvaluateReportVo report = aeaEvaluateReportMapper.selectVoById(reportId);
            if (report == null) {
                throw new RuntimeException("评估报告不存在");
            }
            
            // 构建PDF生成所需的完整数据
            Map<String, String> reportData = buildCompleteReportData(
                report, questionnaire, taskEvaluate, evaluateInfo, elderInfo,
                executionRecords, questionMap, answerMap, institutionName
            );
            
            // 调用原有的PDF生成服务
            return evaluateReportService.generatePdfReport(reportId);
            
        } catch (Exception e) {
            log.error("生成包含完整数据的PDF报告失败，报告ID: {}", reportId, e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建完整的PDF报告数据
     */
    private Map<String, String> buildCompleteReportData(AeaEvaluateReportVo report,
                                                       AeaQuestionnaireVo questionnaire,
                                                       AeaTaskEvaluateVo taskEvaluate,
                                                       AeaTaskEvaluateInfoVo evaluateInfo,
                                                       AeaElderHealthInfoVo elderInfo,
                                                       List<AeaTaskEvaluateExecutionRecordVo> executionRecords,
                                                       Map<Long, AeaQuestionnaireQuestionVo> questionMap,
                                                       Map<Long, List<AeaQuestionnaireAnswerVo>> answerMap,
                                                       String institutionName) {
        Map<String, String> data = new HashMap<>();
        
        // 基本信息
        data.put("code", report.getCode());
        data.put("institutionName", institutionName);
        
        // 被评估人信息
        if (elderInfo != null) {
            data.put("name", elderInfo.getName() != null ? elderInfo.getName() : "未知");
            data.put("gender", elderInfo.getGender() != null ? elderInfo.getGender() : "未知");
            data.put("age", elderInfo.getAge() != null ? elderInfo.getAge().toString() : "未知");
            data.put("idCard", elderInfo.getIdCard() != null ? elderInfo.getIdCard() : "未知");
        } else {
            data.put("name", "未知");
            data.put("gender", "未知");
            data.put("age", "未知");
            data.put("idCard", "未知");
        }
        
        // 评估时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        if (evaluateInfo.getEvaluateTime() != null) {
            data.put("evaBaseTime", sdf.format(evaluateInfo.getEvaluateTime()));
        } else {
            data.put("evaBaseTime", sdf.format(new Date()));
        }
        
        // 分数信息
        data.put("selfScore", report.getSelfScore() != null ? report.getSelfScore().toString() : "0");
        data.put("baseScore", report.getBaseScore() != null ? report.getBaseScore().toString() : "0");
        data.put("mentionScore", report.getMentionScore() != null ? report.getMentionScore().toString() : "0");
        data.put("feelScore", report.getFeelScore() != null ? report.getFeelScore().toString() : "0");
        data.put("totalScore", report.getTotalScore() != null ? report.getTotalScore().toString() : "0");
        
        // 等级信息
        data.put("firstLevel", report.getFirstLevel() != null ? report.getFirstLevel().toString() : "未评级");
        data.put("finalLevel", report.getFinalLevel() != null ? report.getFinalLevel().toString() : "未评级");
        
        // 评估地点
        data.put("location", report.getLocation() != null ? report.getLocation() : "");
        
        // 调整依据
        data.put("adjustmentBasis", report.getAdjustmentBasis() != null ? report.getAdjustmentBasis() : "");
        
        // 评估员信息
        data.put("assessorName", taskEvaluate.getAssessorUserId() != null ? taskEvaluate.getAssessorUserId().toString() : "主评估员");
        data.put("deputyName", taskEvaluate.getDeputyUserId() != null ? taskEvaluate.getDeputyUserId().toString() : "副评估员");
        
        // 答题记录相关数据（包含题目内容和答案内容）
        if (executionRecords != null && !executionRecords.isEmpty()) {
            for (int i = 0; i < executionRecords.size() && i < 50; i++) {
                AeaTaskEvaluateExecutionRecordVo record = executionRecords.get(i);
                
                // 题目内容
                AeaQuestionnaireQuestionVo question = questionMap.get(record.getQuestionId());
                if (question != null) {
                    data.put("question_" + (i + 1), question.getContent() != null ? question.getContent() : "");
                    data.put("questionType_" + (i + 1), question.getType() != null ? question.getType() : "");
                } else {
                    data.put("question_" + (i + 1), "");
                    data.put("questionType_" + (i + 1), "");
                }
                
                // 答案内容
                if (record.getAnswerId() != null) {
                    List<AeaQuestionnaireAnswerVo> answers = answerMap.get(record.getQuestionId());
                    if (answers != null) {
                        AeaQuestionnaireAnswerVo selectedAnswer = answers.stream()
                            .filter(answer -> answer.getId().equals(record.getAnswerId()))
                            .findFirst()
                            .orElse(null);
                        if (selectedAnswer != null) {
                            data.put("answer_" + (i + 1), selectedAnswer.getContent() != null ? selectedAnswer.getContent() : "");
                            data.put("answerScore_" + (i + 1), selectedAnswer.getScore() != null ? selectedAnswer.getScore().toString() : "0");
                        } else {
                            data.put("answer_" + (i + 1), "");
                            data.put("answerScore_" + (i + 1), "0");
                        }
                    } else {
                        data.put("answer_" + (i + 1), "");
                        data.put("answerScore_" + (i + 1), "0");
                    }
                } else {
                    data.put("answer_" + (i + 1), "");
                    data.put("answerScore_" + (i + 1), "0");
                }
                
                // 记录分数
                data.put("score_" + (i + 1), record.getScore() != null ? record.getScore().toString() : "0");
            }
        }
        
        log.info("构建完整PDF报告数据完成，数据项数量: {}", data.size());
        return data;
    }
    
    /**
     * 根据任务ID获取问卷信息
     */
    private AeaQuestionnaireVo getQuestionnaireByTaskId(Long taskId) {
        // 通过任务ID查询评估详情，再通过评估详情获取问卷ID
        LambdaQueryWrapper<AeaTaskEvaluateInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(AeaTaskEvaluateInfo::getTaskId, taskId);
        List<AeaTaskEvaluateInfoVo> evaluateInfoList = aeaTaskEvaluateInfoMapper.selectVoList(queryWrapper);
        
        if (!evaluateInfoList.isEmpty()) {
            Long questionnaireId = evaluateInfoList.get(0).getQuestionnaireId();
            return questionnaireService.queryById(questionnaireId);
        }
        
        return null;
    }
    
    /**
     * 计算自理能力得分
     */
    private Integer calculateSelfScore(List<AeaTaskEvaluateExecutionRecordVo> executionRecords) {
        // 这里需要根据具体的业务逻辑来计算自理能力得分
        // 可以根据问卷类型或题目类型来筛选相关记录
        return executionRecords.stream()
            .filter(record -> record.getQuestionType() != null && record.getQuestionType().contains("自理"))
            .mapToInt(record -> record.getScore() != null ? record.getScore() : 0)
            .sum();
    }
    
    /**
     * 计算基础运动能力得分
     */
    private Integer calculateBaseScore(List<AeaTaskEvaluateExecutionRecordVo> executionRecords) {
        return executionRecords.stream()
            .filter(record -> record.getQuestionType() != null && record.getQuestionType().contains("运动"))
            .mapToInt(record -> record.getScore() != null ? record.getScore() : 0)
            .sum();
    }
    
    /**
     * 计算精神状态得分
     */
    private Integer calculateMentionScore(List<AeaTaskEvaluateExecutionRecordVo> executionRecords) {
        return executionRecords.stream()
            .filter(record -> record.getQuestionType() != null && record.getQuestionType().contains("精神"))
            .mapToInt(record -> record.getScore() != null ? record.getScore() : 0)
            .sum();
    }
    
    /**
     * 计算感知觉与社会参与得分
     */
    private Integer calculateFeelScore(List<AeaTaskEvaluateExecutionRecordVo> executionRecords) {
        return executionRecords.stream()
            .filter(record -> record.getQuestionType() != null && 
                (record.getQuestionType().contains("感知") || record.getQuestionType().contains("社会")))
            .mapToInt(record -> record.getScore() != null ? record.getScore() : 0)
            .sum();
    }
}