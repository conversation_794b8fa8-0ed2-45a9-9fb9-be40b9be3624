package org.dromara.cyly.aea.refactor.service.impl;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.mybatis.core.page.PageQuery;
import org.dromara.common.mybatis.core.page.TableDataInfo;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReport;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateInfo;
import org.dromara.cyly.aea.refactor.domain.bo.AeaEvaluateReportBo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateExecutionRecordVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateVo;
import org.dromara.cyly.aea.refactor.mapper.AeaEvaluateReportMapper;
import org.dromara.cyly.aea.refactor.mapper.AeaTaskEvaluateInfoMapper;
import org.dromara.cyly.aea.refactor.mapper.AeaTaskEvaluateMapper;
import org.dromara.cyly.aea.refactor.service.IAeaEvaluateReportService;
import org.dromara.cyly.aea.refactor.service.IAeaTaskEvaluateExecutionRecordService;
import org.dromara.cyly.aea.refactor.templates.GBReportTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 评估报告
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AeaEvaluateReportServiceImpl implements IAeaEvaluateReportService {

    private final AeaEvaluateReportMapper baseMapper;
    private final IAeaTaskEvaluateExecutionRecordService executionRecordService;
    private final AeaTaskEvaluateMapper aeaTaskEvaluateMapper;
    private final AeaTaskEvaluateInfoMapper aeaTaskEvaluateInfoMapper;
    private final GBReportTemplate gbReportTemplate;

    /**
     * 查询评估报告
     */
    @Override
    public AeaEvaluateReportVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 查询评估报告列表
     */
    @Override
    public TableDataInfo<AeaEvaluateReportVo> queryPageList(AeaEvaluateReportBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<AeaEvaluateReport> lqw = buildQueryWrapper(bo);
        Page<AeaEvaluateReportVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);
        return TableDataInfo.build(result);
    }

    /**
     * 查询评估报告列表
     */
    @Override
    public List<AeaEvaluateReportVo> queryList(AeaEvaluateReportBo bo) {
        LambdaQueryWrapper<AeaEvaluateReport> lqw = buildQueryWrapper(bo);
        return baseMapper.selectVoList(lqw);
    }

    private LambdaQueryWrapper<AeaEvaluateReport> buildQueryWrapper(AeaEvaluateReportBo bo) {
        LambdaQueryWrapper<AeaEvaluateReport> lqw = Wrappers.lambdaQuery();
        lqw.like(StringUtils.isNotBlank(bo.getCode()), AeaEvaluateReport::getCode, bo.getCode());
        lqw.eq(bo.getTaskId() != null, AeaEvaluateReport::getTaskId, bo.getTaskId());
        lqw.eq(bo.getElderId() != null, AeaEvaluateReport::getElderId, bo.getElderId());
        lqw.eq(bo.getAssessorEvaluateUserId() != null, AeaEvaluateReport::getAssessorEvaluateUserId, bo.getAssessorEvaluateUserId());
        lqw.eq(bo.getDeputyEvaluateUserId() != null, AeaEvaluateReport::getDeputyEvaluateUserId, bo.getDeputyEvaluateUserId());
        lqw.like(StringUtils.isNotBlank(bo.getLocation()), AeaEvaluateReport::getLocation, bo.getLocation());
        lqw.like(bo.getReasonCode()!=null, AeaEvaluateReport::getReasonCode, bo.getReasonCode());
        lqw.eq(bo.getSelfScore() != null, AeaEvaluateReport::getSelfScore, bo.getSelfScore());
        lqw.eq(bo.getBaseScore() != null, AeaEvaluateReport::getBaseScore, bo.getBaseScore());
        lqw.eq(bo.getMentionScore() != null, AeaEvaluateReport::getMentionScore, bo.getMentionScore());
        lqw.eq(bo.getFeelScore() != null, AeaEvaluateReport::getFeelScore, bo.getFeelScore());
        lqw.eq(bo.getTotalScore() != null, AeaEvaluateReport::getTotalScore, bo.getTotalScore());
        lqw.like(bo.getFirstLevel()!=null, AeaEvaluateReport::getFirstLevel, bo.getFirstLevel());
        lqw.like(StringUtils.isNotBlank(bo.getAdjustmentBasis()), AeaEvaluateReport::getAdjustmentBasis, bo.getAdjustmentBasis());
        lqw.like(bo.getFinalLevel()!=null, AeaEvaluateReport::getFinalLevel, bo.getFinalLevel());
        lqw.eq(bo.getAnswerId() != null, AeaEvaluateReport::getAnswerId, bo.getAnswerId());
        lqw.like(StringUtils.isNotBlank(bo.getContent()), AeaEvaluateReport::getContent, bo.getContent());
        lqw.eq(bo.getScore() != null, AeaEvaluateReport::getScore, bo.getScore());
        lqw.eq(bo.getEvaluateUserId() != null, AeaEvaluateReport::getEvaluateUserId, bo.getEvaluateUserId());
        lqw.eq(bo.getDelFlag() != null, AeaEvaluateReport::getDelFlag, bo.getDelFlag());
        return lqw;
    }

    /**
     * 新增评估报告
     */
    @Override
    public Boolean insertByBo(AeaEvaluateReportBo bo) {
        AeaEvaluateReport add = MapstructUtils.convert(bo, AeaEvaluateReport.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改评估报告
     */
    @Override
    public Boolean updateByBo(AeaEvaluateReportBo bo) {
        AeaEvaluateReport update = MapstructUtils.convert(bo, AeaEvaluateReport.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(AeaEvaluateReport entity){
        // 可以根据需要添加校验逻辑
    }

    /**
     * 批量删除评估报告
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            // 可以添加删除前的校验逻辑
        }
        return baseMapper.deleteBatchIds(ids) > 0;
    }

    /**
     * 创建评估报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createEvaluateReport(AeaEvaluateReportBo reportBo) {
        // 生成报告编码
        String code = generateReportCode();
        reportBo.setCode(code);
        
        // 设置删除状态为否
        reportBo.setDelFlag(0);
        
        // 计算分数和等级
        calculateScoresAndLevel(reportBo);
        
        // 插入报告记录
        Boolean result = insertByBo(reportBo);
        if (!result) {
            throw new RuntimeException("创建评估报告失败");
        }
        
        return reportBo.getId();
    }

    /**
     * 生成PDF报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generatePdfReport(Long reportId) {
        AeaEvaluateReportVo report = baseMapper.selectVoById(reportId);
        if (report == null) {
            throw new RuntimeException("评估报告不存在");
        }
        
        try {
            // 获取答题记录
            List<AeaTaskEvaluateExecutionRecordVo> records = 
                executionRecordService.queryByTaskIdAndInfoId(report.getTaskId(), report.getId());
            
            // 生成PDF文件
            String pdfUrl = generatePdfFile(report, records);
            
            // 更新报告URL
            updateReportUrl(reportId, pdfUrl);
            
            return pdfUrl;
        } catch (Exception e) {
            log.error("生成PDF报告失败，报告ID: {}", reportId, e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage());
        }
    }

    /**
     * 根据任务ID查询报告
     */
    @Override
    public AeaEvaluateReportVo queryByTaskId(Long taskId) {
        LambdaQueryWrapper<AeaEvaluateReport> lqw = Wrappers.lambdaQuery();
        lqw.eq(AeaEvaluateReport::getTaskId, taskId)
           .eq(AeaEvaluateReport::getDelFlag, 0)
           .orderByDesc(AeaEvaluateReport::getCreateTime)
           .last("LIMIT 1");
        
        return baseMapper.selectVoOne(lqw);
    }

    /**
     * 更新报告URL
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateReportUrl(Long reportId, String reportUrl) {
        AeaEvaluateReport report = baseMapper.selectById(reportId);
        if (report == null) {
            throw new RuntimeException("评估报告不存在");
        }
        
        report.setReportUrl(reportUrl);
        return baseMapper.updateById(report) > 0;
    }

    /**
     * 获取报告下载链接
     */
    @Override
    public String getReportDownloadUrl(Long reportId) {
        AeaEvaluateReportVo report = queryById(reportId);
        if (report == null) {
            throw new RuntimeException("评估报告不存在");
        }
        
        if (StringUtils.isBlank(report.getReportUrl())) {
            // 如果报告URL为空，尝试生成PDF
            return generatePdfReport(reportId);
        }
        
        return report.getReportUrl();
    }

    @Override
    public List<AeaEvaluateReportVo> selectVoListByTaskIds(Set<Long> taskIdList) {
        if (CollectionUtils.isEmpty(taskIdList)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<AeaEvaluateReport> lqw = Wrappers.lambdaQuery();
        lqw.in(AeaEvaluateReport::getTaskId, taskIdList);
        return baseMapper.selectVoList(lqw);
    }

    @Override
    public Long selectCount(String code) {
        return baseMapper.selectCount(new QueryWrapper<AeaEvaluateReport>()
                .like("code", code));
    }

    /**
     * 生成报告编码
     */
    private String generateReportCode() {
        String dateStr = DateUtil.format(new Date(), "yyyyMMdd");
        String uuid = IdUtil.fastSimpleUUID().substring(0, 8).toUpperCase();
        return "RPT" + dateStr + uuid;
    }

    /**
     * 计算分数和等级
     */
    private void calculateScoresAndLevel(AeaEvaluateReportBo reportBo) {
        if (reportBo.getTaskId() == null) {
            return;
        }
        
        // 计算总分
        Integer totalScore = executionRecordService.calculateTotalScore(reportBo.getTaskId(), reportBo.getId());
        reportBo.setTotalScore(totalScore);
        
        // 计算各维度分数
        Map<String, Integer> dimensionScores = 
            executionRecordService.calculateDimensionScores(reportBo.getTaskId(), reportBo.getId());
        
        // 设置各维度分数（根据实际维度调整）
        reportBo.setBaseScore(dimensionScores.getOrDefault("基础能力", 0));
        reportBo.setMentionScore(dimensionScores.getOrDefault("认知能力", 0));
        reportBo.setFeelScore(dimensionScores.getOrDefault("情感能力", 0));
        reportBo.setSelfScore(dimensionScores.getOrDefault("其他", 0));
        
        // 计算等级
        String levelStr = executionRecordService.calculateLevel(totalScore);
        Integer level = convertLevelToInteger(levelStr);
        reportBo.setFirstLevel(level);
        reportBo.setFinalLevel(level);
    }

    /**
     * 将等级字符串转换为整数
     */
    private Integer convertLevelToInteger(String levelStr) {
        if (StringUtils.isBlank(levelStr)) {
            return 1; // 默认为能力完好
        }
        
        switch (levelStr) {
            case "优秀":
            case "能力完好":
                return 1;
            case "良好":
            case "轻度受损":
                return 2;
            case "中等":
            case "中度受损":
                return 3;
            case "及格":
            case "重度受损":
                return 4;
            case "不及格":
            case "完全丧失":
                return 5;
            default:
                return 1; // 默认为能力完好
        }
    }

    /**
     * 生成PDF文件
     * 集成GBReportTemplate实现真正的PDF生成
     */
    private String generatePdfFile(AeaEvaluateReportVo report, List<AeaTaskEvaluateExecutionRecordVo> records) {
        try {
            log.info("开始生成PDF报告，报告编码: {}", report.getCode());
            
            // 1. 获取任务和评估详情信息
            AeaTaskEvaluateVo task = aeaTaskEvaluateMapper.selectVoById(report.getTaskId());
            LambdaQueryWrapper<AeaTaskEvaluateInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AeaTaskEvaluateInfo::getTaskId, report.getTaskId());
            List<AeaTaskEvaluateInfoVo> evaluateInfoVoList = aeaTaskEvaluateInfoMapper.selectVoList(queryWrapper);

            if (task == null || evaluateInfoVoList.isEmpty()) {
                throw new RuntimeException("任务或评估详情信息不存在");
            }
            
            // 2. 构建PDF生成所需的数据
            Map<String, String> reportData = buildReportData(report, task, evaluateInfoVoList, records);
            
            // 3. 调用GBReportTemplate生成PDF
            File pdfFile = gbReportTemplate.generateReportExec(
                reportData,
                "康养机构", // 机构名称，可以从配置或数据库获取
                report.getAssessorEvaluateUserId() != null ? report.getAssessorEvaluateUserId().toString() : "主评估员",
                report.getDeputyEvaluateUserId() != null ? report.getDeputyEvaluateUserId().toString() : "副评估员",
                report.getLocation() != null ? report.getLocation() : "评估地点",
                new ArrayList<>(), // 药物列表，根据实际需求填充
                report.getAssessorSign(), // 主评估员签名
                report.getDeputySign(),   // 副评估员签名
                report.getInformationProviderSign()    // 信息提供者签名
            );
            
            // 4. 生成文件访问URL
            String fileName = pdfFile.getName();
            String pdfUrl = "/uploads/reports/" + fileName;
            
            log.info("PDF报告生成成功，报告编码: {}, 文件路径: {}", report.getCode(), pdfUrl);
            
            return pdfUrl;
            
        } catch (Exception e) {
            log.error("生成PDF报告失败，报告编码: {}", report.getCode(), e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建PDF报告生成所需的数据 - 严格按照五个数据块结构组织
     */
    private Map<String, String> buildReportData(AeaEvaluateReportVo report, AeaTaskEvaluateVo task,
                                               List<AeaTaskEvaluateInfoVo> evaluateInfoVoList,
                                               List<AeaTaskEvaluateExecutionRecordVo> records) {
        Map<String, String> data = new HashMap<>();
        
        try {
            log.info("开始构建PDF报告数据，严格按照五个数据块结构组织");
            
            /*-------------------------第一节PDF数据----------------------------------------*/
            // 获取老年人能力评估基本信息表信息列表
            // 获取任务记录表->表 A.1 评估信息表所需填充数据
            log.info("第一节：构建评估信息表数据");
            data.put("reportCode", report.getCode() != null ? report.getCode() : "");
            data.put("taskId", task.getId() != null ? task.getId().toString() : "");
            data.put("taskType", task.getTaskType() != null ? task.getTaskType().toString() : "");
            data.put("taskStatus", task.getStatus() != null ? task.getStatus().toString() : "");
            
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            data.put("expectedStartTime", task.getExpectedStartTime() != null ? sdf.format(task.getExpectedStartTime()) : "");
            data.put("expectedEndTime", task.getExpectedEndTime() != null ? sdf.format(task.getExpectedEndTime()) : "");
            data.put("actualStartTime", task.getStartTime() != null ? sdf.format(task.getStartTime()) : "");
            data.put("actualEndTime", task.getEndTime() != null ? sdf.format(task.getEndTime()) : "");
            
            /*-------------------------第二节PDF数据----------------------------------------*/
            // 获取老年人能力评估基本信息表->评估对象基本信息表所需填充数据
            // 包含：表 A.2 评估对象基本信息表、表 A.3 信息提供者及联系人信息表、表 A.4 疾病诊断和用药情况表、表 A.5 健康相关问题
            log.info("第二节：构建评估对象基本信息表数据");
            
            // 从执行记录中提取基本信息表相关数据
            if (records != null && !records.isEmpty()) {
                // 按问卷类型分组处理基本信息表数据
                Map<String, List<AeaTaskEvaluateExecutionRecordVo>> basicInfoRecords = records.stream()
                    .filter(record -> isBasicInfoRecord(record))
                    .collect(Collectors.groupingBy(this::getRecordCategory));
                
                // A.2 评估对象基本信息表
                List<AeaTaskEvaluateExecutionRecordVo> objectInfoRecords = basicInfoRecords.get("评估对象基本信息表");
                if (objectInfoRecords != null) {
                    for (int i = 0; i < objectInfoRecords.size(); i++) {
                        AeaTaskEvaluateExecutionRecordVo record = objectInfoRecords.get(i);
                        data.put("objectInfo_question_" + (i + 1), record.getContent() != null ? record.getContent() : "");
                        data.put("objectInfo_answer_" + (i + 1), record.getAnswerId() != null ? record.getAnswerId().toString() : "");
                    }
                }
                
                // A.3 信息提供者及联系人信息表
                List<AeaTaskEvaluateExecutionRecordVo> providerInfoRecords = basicInfoRecords.get("信息提供者及联系人信息表");
                if (providerInfoRecords != null) {
                    for (int i = 0; i < providerInfoRecords.size(); i++) {
                        AeaTaskEvaluateExecutionRecordVo record = providerInfoRecords.get(i);
                        data.put("providerInfo_question_" + (i + 1), record.getContent() != null ? record.getContent() : "");
                        data.put("providerInfo_answer_" + (i + 1), record.getAnswerId() != null ? record.getAnswerId().toString() : "");
                    }
                }
                
                // A.4 疾病诊断和用药情况表
                List<AeaTaskEvaluateExecutionRecordVo> diseaseInfoRecords = basicInfoRecords.get("疾病诊断和用药情况表");
                if (diseaseInfoRecords != null) {
                    for (int i = 0; i < diseaseInfoRecords.size(); i++) {
                        AeaTaskEvaluateExecutionRecordVo record = diseaseInfoRecords.get(i);
                        data.put("diseaseInfo_question_" + (i + 1), record.getContent() != null ? record.getContent() : "");
                        data.put("diseaseInfo_answer_" + (i + 1), record.getAnswerId() != null ? record.getAnswerId().toString() : "");
                    }
                }
                
                // A.5 健康相关问题
                List<AeaTaskEvaluateExecutionRecordVo> healthInfoRecords = basicInfoRecords.get("健康相关问题");
                if (healthInfoRecords != null) {
                    for (int i = 0; i < healthInfoRecords.size(); i++) {
                        AeaTaskEvaluateExecutionRecordVo record = healthInfoRecords.get(i);
                        data.put("healthInfo_question_" + (i + 1), record.getContent() != null ? record.getContent() : "");
                        data.put("healthInfo_answer_" + (i + 1), record.getAnswerId() != null ? record.getAnswerId().toString() : "");
                    }
                }
            }
            
            /*--------------------------第三节PDF数据---------------------------------*/
            // 获取老年人能力评估表问卷问题所答信息
            // 包含：表 B.1 老年人能力评估表、表B.2 基础运动能力评估表、表 B.3 精神状态评估表、表B.4 感知觉与社会参与评估表
            log.info("第三节：构建老年人能力评估表数据");
            
            if (records != null && !records.isEmpty()) {
                // 按评估模块分组处理评估表数据
                Map<String, List<AeaTaskEvaluateExecutionRecordVo>> assessmentRecords = records.stream()
                    .filter(record -> isAssessmentRecord(record))
                    .collect(Collectors.groupingBy(this::getAssessmentCategory));
                
                // B.1 自理能力评估表
                List<AeaTaskEvaluateExecutionRecordVo> selfCareRecords = assessmentRecords.get("自理能力评估");
                if (selfCareRecords != null) {
                    for (int i = 0; i < selfCareRecords.size(); i++) {
                        AeaTaskEvaluateExecutionRecordVo record = selfCareRecords.get(i);
                        data.put("selfCare_question_" + (i + 1), record.getContent() != null ? record.getContent() : "");
                        data.put("selfCare_answer_" + (i + 1), record.getAnswerId() != null ? record.getAnswerId().toString() : "");
                        data.put("selfCare_score_" + (i + 1), record.getScore() != null ? record.getScore().toString() : "0");
                    }
                }
                
                // B.2 基础运动能力评估表
                List<AeaTaskEvaluateExecutionRecordVo> baseMotorRecords = assessmentRecords.get("基础运动能力评估表");
                if (baseMotorRecords != null) {
                    for (int i = 0; i < baseMotorRecords.size(); i++) {
                        AeaTaskEvaluateExecutionRecordVo record = baseMotorRecords.get(i);
                        data.put("baseMotor_question_" + (i + 1), record.getContent() != null ? record.getContent() : "");
                        data.put("baseMotor_answer_" + (i + 1), record.getAnswerId() != null ? record.getAnswerId().toString() : "");
                        data.put("baseMotor_score_" + (i + 1), record.getScore() != null ? record.getScore().toString() : "0");
                    }
                }
                
                // B.3 精神状态评估表
                List<AeaTaskEvaluateExecutionRecordVo> mentalStateRecords = assessmentRecords.get("精神状态评估表");
                if (mentalStateRecords != null) {
                    for (int i = 0; i < mentalStateRecords.size(); i++) {
                        AeaTaskEvaluateExecutionRecordVo record = mentalStateRecords.get(i);
                        data.put("mentalState_question_" + (i + 1), record.getContent() != null ? record.getContent() : "");
                        data.put("mentalState_answer_" + (i + 1), record.getAnswerId() != null ? record.getAnswerId().toString() : "");
                        data.put("mentalState_score_" + (i + 1), record.getScore() != null ? record.getScore().toString() : "0");
                    }
                }
                
                // B.4 感知觉与社会参与评估表
                List<AeaTaskEvaluateExecutionRecordVo> socialParticipationRecords = assessmentRecords.get("感知觉与社会参与");
                if (socialParticipationRecords != null) {
                    for (int i = 0; i < socialParticipationRecords.size(); i++) {
                        AeaTaskEvaluateExecutionRecordVo record = socialParticipationRecords.get(i);
                        data.put("socialParticipation_question_" + (i + 1), record.getContent() != null ? record.getContent() : "");
                        data.put("socialParticipation_answer_" + (i + 1), record.getAnswerId() != null ? record.getAnswerId().toString() : "");
                        data.put("socialParticipation_score_" + (i + 1), record.getScore() != null ? record.getScore().toString() : "0");
                    }
                }
            }
            
            /*--------------------------第四节PDF数据---------------------------------*/
            // 评估报告基本信息和分数统计
            log.info("第四节：构建评估报告基本信息和分数统计数据");
            
            // 分数信息
            data.put("selfScore", report.getSelfScore() != null ? report.getSelfScore().toString() : "0");
            data.put("baseScore", report.getBaseScore() != null ? report.getBaseScore().toString() : "0");
            data.put("mentionScore", report.getMentionScore() != null ? report.getMentionScore().toString() : "0");
            data.put("feelScore", report.getFeelScore() != null ? report.getFeelScore().toString() : "0");
            data.put("totalScore", report.getTotalScore() != null ? report.getTotalScore().toString() : "0");
            
            // 等级信息
            data.put("firstLevel", report.getFirstLevel() != null ? report.getFirstLevel().toString() : "未评级");
            data.put("finalLevel", report.getFinalLevel() != null ? report.getFinalLevel().toString() : "未评级");
            
            // 评估地点和调整依据
            data.put("location", report.getLocation() != null ? report.getLocation() : "");
            data.put("adjustmentBasis", report.getAdjustmentBasis() != null ? report.getAdjustmentBasis() : "");
            
            // 评估员信息
            data.put("assessorEvaluateUserId", report.getAssessorEvaluateUserId() != null ? report.getAssessorEvaluateUserId().toString() : "");
            data.put("deputyEvaluateUserId", report.getDeputyEvaluateUserId() != null ? report.getDeputyEvaluateUserId().toString() : "");
            
            // 签名信息
            data.put("assessorSign", report.getAssessorSign() != null ? report.getAssessorSign() : "");
            data.put("deputySign", report.getDeputySign() != null ? report.getDeputySign() : "");
            data.put("informationProviderSign", report.getInformationProviderSign() != null ? report.getInformationProviderSign() : "");
            
            /*----------------------------------第五节------------------------------------*/
            // 最终报告汇总信息
            log.info("第五节：构建最终报告汇总信息");
            
            // 评估时间
            SimpleDateFormat reportSdf = new SimpleDateFormat("yyyy年MM月dd日");
            data.put("evaluationDate", reportSdf.format(new Date()));
            
            // 报告生成时间
            data.put("reportGenerateTime", sdf.format(new Date()));
            
            // 评估原因
            data.put("reasonCode", report.getReasonCode() != null ? getReasonDescription(report.getReasonCode()) : "");
            
            // 长者信息（如果有的话）
            data.put("elderId", report.getElderId() != null ? report.getElderId().toString() : "");
            
            log.info("构建PDF报告数据完成，严格按照五个数据块结构，数据项数量: {}", data.size());
            return data;
            
        } catch (Exception e) {
            log.error("构建PDF报告数据失败", e);
            throw new RuntimeException("构建PDF报告数据失败: " + e.getMessage());
        }
    }
    
    /**
     * 判断是否为基本信息记录
     */
    private boolean isBasicInfoRecord(AeaTaskEvaluateExecutionRecordVo record) {
        // 根据问题ID或其他标识判断是否属于基本信息表
        // 这里需要根据实际的问卷结构来判断
        return true; // 临时返回true，需要根据实际情况调整
    }
    
    /**
     * 判断是否为评估记录
     */
    private boolean isAssessmentRecord(AeaTaskEvaluateExecutionRecordVo record) {
        // 根据问题ID或其他标识判断是否属于评估表
        // 这里需要根据实际的问卷结构来判断
        return true; // 临时返回true，需要根据实际情况调整
    }
    
    /**
     * 获取记录分类（基本信息表）
     */
    private String getRecordCategory(AeaTaskEvaluateExecutionRecordVo record) {
        // 根据问题ID或其他标识返回具体的表类型
        // 这里需要根据实际的问卷结构来判断
        return "评估对象基本信息表"; // 临时返回，需要根据实际情况调整
    }
    
    /**
     * 获取评估分类
     */
    private String getAssessmentCategory(AeaTaskEvaluateExecutionRecordVo record) {
        // 根据问题ID或其他标识返回具体的评估模块
        // 这里需要根据实际的问卷结构来判断
        return "自理能力评估"; // 临时返回，需要根据实际情况调整
    }
    
    /**
     * 获取评估原因描述
     */
    private String getReasonDescription(Integer reasonCode) {
        switch (reasonCode) {
            case 1: return "首次评估";
            case 2: return "常规评估";
            case 3: return "即时评估";
            case 4: return "复评";
            case 5: return "退出服务评估";
            case 6: return "跟进评估";
            case 7: return "其他";
            default: return "未知";
        }
    }
}
