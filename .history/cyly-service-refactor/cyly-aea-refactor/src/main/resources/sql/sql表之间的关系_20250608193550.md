1. 创建任务时使用aea_task_evaluate.sql
2. aea_task_evaluate_info.sql用于记录aea_questionnaire.sql问卷表中记录选择的问卷下有哪些模块，
所以关联任务记录id和评估问卷id，并统计每个模块的分数，同时修改每个模块的评估状态
3. aea_task_evaluate_execution_record.sql(评估任务执行表)用于接收用户记录的答案和选项，根据任务业务详情表id来判断是哪个模块的，
task_id则是用于查询是哪张问卷，question_id用于查询问题表评估问卷问题表aea_questionnaire_question.sql，answer_id用于评估问卷答案表aea_questionnaire_answer.sql
4. aea_evaluate_report.sql评估报告表用于存储统计得分、签名、主评估员、副评估员、关联长者基础信息表id、总分、评估地点等，用于第四节数据pdf生成
5. aea_questionnaire.sql评估问卷表
6. aea_questionnaire_question.sql评估问卷问题表
7. aea_questionnaire_answer.sql评估问卷答案表