## SQL表关系及字段说明

### 1. `aea_task_evaluate.sql` - 任务记录表

**描述**: 创建任务时使用。

**字段列表**:

| 字段名                      | 类型                                                       | 描述                                                                                                |
| --------------------------- | ---------------------------------------------------------- | --------------------------------------------------------------------------------------------------- |
| `id`                        | bigint                                                     | 主键                                                                                                |
| `dept_id`                   | bigint                                                     | 部门id                                                                                              |
| `task_num`                  | varchar(255)                                               | 任务编号                                                                                              |
| `assessor_evaluate_user_id` | bigint                                                     | 主评估员用户id(app_user表)                                                                            |
| `vice_evaluate_user_id`     | bigint                                                     | 副评估员用户id(app_user表)                                                                            |
| `elder_id`                  | bigint                                                     | 长者用户id(aea_elder表)                                                                               |
| `scheme_code`               | bigint                                                     | 评估问卷表id(aea_questionnaire表的id)                                                                 |
| `reason_code`               | int                                                        | 评估原因  1首次评估  2常规评估  3即时评估  4因对评估结果有疑问进行复评  5退出服务评估-包括服务最后三天   6退出服务跟进评估  7其他-例如上诉、研究 |
| `expected_start_time`       | datetime                                                   | 期望开始时间                                                                                            |
| `expected_end_time`         | datetime                                                   | 期望完成时间                                                                                            |
| `plan_end_time`             | datetime                                                   | 评估员计划完成时间                                                                                        |
| `start_time`                | datetime                                                   | 任务实际开始时间                                                                                          |
| `end_time`                  | datetime                                                   | 任务实际结束时间                                                                                          |
| `start_location`            | varchar(255)                                               | 任务签到地址                                                                                              |
| `start_mark_url`            | varchar(255)                                               | 任务签到多媒体信息存放的url地址                                                                                 |
| `status`                    | tinyint                                                    | 任务状态: 0-待执行, 1-执行中, 2-执行成功, 3-执行失败                                                                  |
| `audit_status`              | tinyint                                                    | 审核状态: 0-无审核, 1-待审核, 2-审核通过, 3-审核驳回                                                                  |
| `source`                    | tinyint                                                    | 数据来源: \n        0-评估师, \n        1-平台, \n        2-民政                                                  |
| `del_flag`                  | tinyint                                                    | 删除状态: 0-否, 1-是                                                                                      |
| `remark`                    | varchar(255)                                               | 备注信息                                                                                              |
| `assign_user_id`            | bigint                                                     | 进行分派操作的用户id                                                                                      |
| `assign_source`             | tinyint                                                    | 进行分派操作的用户来源 0：评估员 1：机构 2：民政                                                                      |
| `assign_time`               | datetime                                                   | 分派时间                                                                                              |
| `assign_dept_time`          | datetime                                                   | 分派到机构时间                                                                                            |
| `create_dept`               | bigint                                                     | 创建机构                                                                                              |
| `create_by`                 | bigint                                                     | 创建者id                                                                                              |
| `create_time`               | datetime                                                   | 创建时间                                                                                              |
| `update_by`                 | bigint                                                     | 更新者id                                                                                              |
| `update_time`               | datetime                                                   | 更新时间                                                                                              |

---

### 2. `aea_task_evaluate_info.sql` - 评估任务模块记录表

**描述**: 用于记录 `aea_questionnaire.sql` 问卷表中选择的问卷下有哪些模块，所以关联任务记录id和评估问卷id，并统计每个模块的分数，同时修改每个模块的评估状态。

**字段列表**:

| 字段名             | 类型                                                       | 描述                                                              |
| ------------------ | ---------------------------------------------------------- | ----------------------------------------------------------------- |
| `id`               | bigint                                                     | 主键                                                              |
| `task_id`          | bigint                                                     | 关联任务记录表id                                                      |
| `questionnaire_id` | bigint                                                     | 关联评估问卷表id                                                      |
| `evaluate_user_id` | bigint                                                     | 执行评估的用户id（app_user）                                          |
| `total_score`      | int                                                        | 提交得分总分                                                          |
| `status`           | tinyint                                                    | 评估状态: 0-待开始, 1-执行中, 2-执行成功, 3-执行失败                      |
| `remark`           | varchar(255)                                               | 备注                                                              |
| `del_flag`         | tinyint                                                    | 删除状态: 0-否, 1-是                                                |
| `start_time`       | datetime                                                   | 评估开始时间                                                          |
| `end_time`         | datetime                                                   | 评估完成时间                                                          |
| `create_dept`      | bigint                                                     | 创建部门                                                              |
| `create_by`        | bigint                                                     | 创建者id                                                            |
| `create_time`      | datetime                                                   | 创建时间                                                              |
| `update_by`        | bigint                                                     | 更新者id                                                            |
| `update_time`      | datetime                                                   | 更新时间                                                              |

---

### 3. `aea_task_evaluate_execution_record.sql` - 评估任务执行表

**描述**: 用于接收用户记录的答案和选项，根据任务业务详情表id (`info_id`) 来判断是哪个模块的，`task_id` 则是用于查询是哪张问卷，`question_id` 用于查询问题表 `aea_questionnaire_question.sql`，`answer_id` 用于评估问卷答案表 `aea_questionnaire_answer.sql`。

**字段列表**:

| 字段名        | 类型                                                       | 描述                                                                                             |
| ------------- | ---------------------------------------------------------- | ------------------------------------------------------------------------------------------------ |
| `id`          | bigint                                                     | 主键                                                                                             |
| `info_id`     | bigint                                                     | 关联任务业务详情表id                                                                                 |
| `task_id`     | bigint                                                     | 关联任务记录表id                                                                                     |
| `question_id` | bigint                                                     | 关联评估问卷问题表id                                                                                 |
| `answer_id`   | bigint                                                     | 关联评估问卷答案表id                                                                                 |
| `content`     | varchar(255)                                               | 用户输入的文本内容（非选项答案时填写）                                                                         |
| `score`       | int                                                        | 评分值                                                                                             |
| `batch_num`   | int                                                        | 批次号, 针对一个问题分成子问题, 并且可以多次作答是, 例如(用药填写, 需要写多种药物和服用方法等)                                           |
| `del_flag`    | tinyint                                                    | 删除状态: 0-否, 1-是                                                                               |
| `create_dept` | bigint                                                     | 创建机构                                                                                             |
| `create_by`   | bigint                                                     | 创建者id（app_user）                                                                               |
| `create_time` | datetime                                                   | 创建时间                                                                                             |
| `update_by`   | bigint                                                     | 更新者id                                                                                             |
| `update_time` | datetime                                                   | 更新时间                                                                                             |

---

### 4. `aea_evaluate_report.sql` - 评估报告表

**描述**: 用于存储统计得分、签名、主评估员、副评估员、关联长者基础信息表id、总分、评估地点等，用于第四节数据pdf生成。

**字段列表**:

| 字段名                        | 类型                                                       | 描述                                                                                                                               |
| ----------------------------- | ---------------------------------------------------------- | ---------------------------------------------------------------------------------------------------------------------------------- |
| `id`                          | bigint                                                     | 主键                                                                                                                               |
| `code`                        | varchar(100)                                               | 评估报告唯一编号（如：AE2023100001）                                                                                                   |
| `task_id`                     | bigint                                                     | 关联任务记录表id                                                                                                                     |
| `elder_id`                    | bigint                                                     | 关联长者基础信息表id                                                                                                                   |
| `assessor_evaluate_user_id`   | bigint                                                     | 主评估员用户id                                                                                                                       |
| `deputy_evaluate_user_id`     | bigint                                                     | 副评估员用户id                                                                                                                       |
| `assessor_sign`               | longtext                                                   | 主评估员签名图片OSS地址                                                                                                                |
| `deputy_sign`                 | longtext                                                   | 副评估员签名图片OSS地址                                                                                                                |
| `information_provider_sign`   | longtext                                                   | 信息提供者签名图片OSS地址                                                                                                              |
| `location`                    | varchar(200)                                               | 评估地点（如：XX社区服务中心）                                                                                                           |
| `reason_code`                 | tinyint                                                    | 评估原因: 1-首次评估, 2-常规评估, 3-即时评估, 4-因对评估结果有疑问进行复评, 5-退出服务评估（服务最后三天）, 6-退出服务跟进评估, 7-其他（如上诉、研究等） |
| `report_url`                  | longtext                                                   | 评估报告文件OSS存储地址                                                                                                                |
| `self_score`                  | int                                                        | 自理能力得分（0~100分）                                                                                                              |
| `base_score`                  | int                                                        | 基础运动能力得分（0~100分）                                                                                                            |
| `mention_score`               | int                                                        | 精神状态得分（0~100分）                                                                                                              |
| `feel_score`                  | int                                                        | 感知觉与社会参与得分（0~100分）                                                                                                          |
| `total_score`                 | int                                                        | 总分（各维度得分之和）                                                                                                                 |
| `first_level`                 | int                                                        | 初步评估等级: 1-能力完好, 2-能力轻度受损, 3-能力中度受损, 4-能力重度受损, 5-能力完全丧失                                                                  |
| `adjustment_basis`            | varchar(50)                                                | 能力等级调整依据: 1-处于昏迷状态直接评定为完全失能, 2-确诊痴呆或精神障碍疾病提高一个等级, 3-近30天发生2次及以上照护风险事件提高一个等级                                 |
| `final_level`                 | int                                                        | 最终评估等级: 1-能力完好, 2-能力轻度受损, 3-能力中度受损, 4-能力重度受损, 5-能力完全丧失                                                                  |
| `answer_id`                   | bigint                                                     | 关联评估问卷答案表id（用于回溯答案）                                                                                                       |
| `content`                     | varchar(200)                                               | 用户输入的补充文本（如开放性问题回答）                                                                                                     |
| `score`                       | int                                                        | 自定义评分（如特殊项打分）                                                                                                               |
| `evaluate_user_id`            | bigint                                                     | 最终提交评估报告的用户id                                                                                                               |
| `del_flag`                    | tinyint                                                    | 删除状态: 0-否, 1-是                                                                                                                   |
| `create_dept`                 | bigint                                                     | 创建机构                                                                                                                             |
| `create_by`                   | bigint                                                     | 创建者id                                                                                                                             |
| `create_time`                 | datetime                                                   | 创建时间                                                                                                                             |
| `update_by`                   | bigint                                                     | 更新者id                                                                                                                             |
| `update_time`                 | datetime                                                   | 更新时间                                                                                                                             |

---

### 5. `aea_questionnaire.sql` - 评估问卷表

**描述**: 存储评估问卷的基本信息和层级结构。

**字段列表**:

| 字段名        | 类型                                                       | 描述                                              |
| ------------- | ---------------------------------------------------------- | ------------------------------------------------- |
| `id`          | bigint                                                     | 主键                                              |
| `title`       | varchar(50)                                                | 问卷标题（如：老年人能力评估标准问卷V2.0）              |
| `parent_id`   | bigint                                                     | 父问卷id（0表示根问卷）                               |
| `sort`        | int                                                        | 排序编号（同级问卷显示顺序）                            |
| `type`        | tinyint                                                    | 类型： 1：目录 2：问卷                               |
| `status`      | tinyint                                                    | 状态: 0-禁用, 1-启用                                |
| `show_type`   | tinyint                                                    | 展示类型        0列表,      1单个,        2自定义     |
| `route`       | varchar(50)                                                | 路由跳转路径                                        |
| `is_del`      | tinyint                                                    | 删除状态: 0-否, 1-是                                |
| `create_dept` | bigint                                                     | 创建部门                                            |
| `create_by`   | bigint                                                     | 创建者id                                          |
| `create_time` | datetime                                                   | 创建时间                                            |
| `update_by`   | bigint                                                     | 更新者id                                          |
| `update_time` | datetime                                                   | 更新时间                                            |

---

### 6. `aea_questionnaire_question.sql` - 评估问卷问题表

**描述**: 存储评估问卷中的具体问题。

**字段列表**:

| 字段名           | 类型                                                       | 描述                                                                                                         |
| ---------------- | ---------------------------------------------------------- | ------------------------------------------------------------------------------------------------------------ |
| `id`             | bigint                                                     | 主键                                                                                                         |
| `questionnaire_id` | bigint                                                     | 关联评估问卷表id                                                                                                 |
| `parent_id`      | bigint                                                     | 父问题id（0表示根问题）                                                                                            |
| `content`        | varchar(200)                                               | 问题内容（如：您是否能独立完成穿衣？）                                                                                     |
| `answer_type`    | tinyint                                                    | 答案类型: 0-无答案（作为分组标题）, 1-单选题, 2-多选题, 3-填空题, 4-视频题, 5-音频题, 6-图片题, 7-日期题, 8-地址题 |
| `sort`           | int                                                        | 排序编号（同级问题显示顺序）                                                                                         |
| `is_required`    | tinyint                                                    | 是否必填: 0-否, 1-是                                                                                           |
| `is_del`         | tinyint                                                    | 删除状态: 0-否, 1-是                                                                                           |
| `create_dept`    | bigint                                                     | 创建部门                                                                                                       |
| `create_by`      | bigint                                                     | 创建者id                                                                                                     |
| `create_time`    | datetime                                                   | 创建时间                                                                                                       |
| `update_by`      | bigint                                                     | 更新者id                                                                                                     |
| `update_time`    | datetime                                                   | 更新时间                                                                                                       |

---

### 7. `aea_questionnaire_answer.sql` - 评估问卷答案表

**描述**: 存储评估问卷问题的答案选项。

**注意**: 由于文件过大，未能完整读取 `aea_questionnaire_answer.sql` 的所有字段。以下为根据常见表结构推断或已知的部分核心字段，可能不完整或不完全准确。

**可能包含的字段列表 (推断)**:

| 字段名        | 类型         | 描述                                         |
| ------------- | ------------ | -------------------------------------------- |
| `id`          | bigint       | 主键                                         |
| `question_id` | bigint       | 关联评估问卷问题表id                             |
| `content`     | varchar(255) | 答案选项内容                                   |
| `score`       | int          | 该选项对应的分数                               |
| `sort`        | int          | 排序编号                                     |
| `is_correct`  | tinyint      | 是否为正确答案 (如果适用，例如测试类问卷)        |
| `is_del`      | tinyint      | 删除状态: 0-否, 1-是                         |
| `create_by`   | bigint       | 创建者id                                     |
| `create_time` | datetime     | 创建时间                                     |
| `update_by`   | bigint       | 更新者id                                     |
| `update_time` | datetime     | 更新时间                                     |