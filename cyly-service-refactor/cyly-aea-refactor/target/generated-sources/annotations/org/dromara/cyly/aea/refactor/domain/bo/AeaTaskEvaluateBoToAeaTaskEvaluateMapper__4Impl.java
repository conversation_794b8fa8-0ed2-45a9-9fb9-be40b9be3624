package org.dromara.cyly.aea.refactor.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluate;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:49:08+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskEvaluateBoToAeaTaskEvaluateMapper__4Impl implements AeaTaskEvaluateBoToAeaTaskEvaluateMapper__4 {

    @Override
    public AeaTaskEvaluate convert(AeaTaskEvaluateBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTaskEvaluate aeaTaskEvaluate = new AeaTaskEvaluate();

        aeaTaskEvaluate.setCreateBy( arg0.getCreateBy() );
        aeaTaskEvaluate.setCreateDept( arg0.getCreateDept() );
        aeaTaskEvaluate.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaTaskEvaluate.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaTaskEvaluate.setSearchValue( arg0.getSearchValue() );
        aeaTaskEvaluate.setUpdateBy( arg0.getUpdateBy() );
        aeaTaskEvaluate.setUpdateTime( arg0.getUpdateTime() );
        aeaTaskEvaluate.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        aeaTaskEvaluate.setAssignDeptTime( arg0.getAssignDeptTime() );
        aeaTaskEvaluate.setAssignSource( arg0.getAssignSource() );
        aeaTaskEvaluate.setAssignTime( arg0.getAssignTime() );
        aeaTaskEvaluate.setAssignUserId( arg0.getAssignUserId() );
        aeaTaskEvaluate.setAuditStatus( arg0.getAuditStatus() );
        aeaTaskEvaluate.setDelFlag( arg0.getDelFlag() );
        aeaTaskEvaluate.setDeptId( arg0.getDeptId() );
        aeaTaskEvaluate.setElderId( arg0.getElderId() );
        aeaTaskEvaluate.setEndTime( arg0.getEndTime() );
        aeaTaskEvaluate.setExpectedEndTime( arg0.getExpectedEndTime() );
        aeaTaskEvaluate.setExpectedStartTime( arg0.getExpectedStartTime() );
        aeaTaskEvaluate.setId( arg0.getId() );
        aeaTaskEvaluate.setPlanEndTime( arg0.getPlanEndTime() );
        aeaTaskEvaluate.setReasonCode( arg0.getReasonCode() );
        aeaTaskEvaluate.setRemark( arg0.getRemark() );
        aeaTaskEvaluate.setSchemeCode( arg0.getSchemeCode() );
        aeaTaskEvaluate.setSource( arg0.getSource() );
        aeaTaskEvaluate.setStartLocation( arg0.getStartLocation() );
        aeaTaskEvaluate.setStartMarkUrl( arg0.getStartMarkUrl() );
        aeaTaskEvaluate.setStartTime( arg0.getStartTime() );
        aeaTaskEvaluate.setStatus( arg0.getStatus() );
        aeaTaskEvaluate.setTaskNum( arg0.getTaskNum() );
        aeaTaskEvaluate.setViceEvaluateUserId( arg0.getViceEvaluateUserId() );

        return aeaTaskEvaluate;
    }

    @Override
    public AeaTaskEvaluate convert(AeaTaskEvaluateBo arg0, AeaTaskEvaluate arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        arg1.setAssignDeptTime( arg0.getAssignDeptTime() );
        arg1.setAssignSource( arg0.getAssignSource() );
        arg1.setAssignTime( arg0.getAssignTime() );
        arg1.setAssignUserId( arg0.getAssignUserId() );
        arg1.setAuditStatus( arg0.getAuditStatus() );
        arg1.setDelFlag( arg0.getDelFlag() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setElderId( arg0.getElderId() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setExpectedEndTime( arg0.getExpectedEndTime() );
        arg1.setExpectedStartTime( arg0.getExpectedStartTime() );
        arg1.setId( arg0.getId() );
        arg1.setPlanEndTime( arg0.getPlanEndTime() );
        arg1.setReasonCode( arg0.getReasonCode() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSchemeCode( arg0.getSchemeCode() );
        arg1.setSource( arg0.getSource() );
        arg1.setStartLocation( arg0.getStartLocation() );
        arg1.setStartMarkUrl( arg0.getStartMarkUrl() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTaskNum( arg0.getTaskNum() );
        arg1.setViceEvaluateUserId( arg0.getViceEvaluateUserId() );

        return arg1;
    }
}
