package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaGbMedicineBoToAeaGbMedicineMapper__1;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVoToAeaGbMedicineMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaGbMedicineVoToAeaGbMedicineMapper__1.class,AeaGbMedicineBoToAeaGbMedicineMapper__1.class},
    imports = {}
)
public interface AeaGbMedicineToAeaGbMedicineVoMapper__1 extends BaseMapper<AeaGbMedicine, AeaGbMedicineVo> {
}
