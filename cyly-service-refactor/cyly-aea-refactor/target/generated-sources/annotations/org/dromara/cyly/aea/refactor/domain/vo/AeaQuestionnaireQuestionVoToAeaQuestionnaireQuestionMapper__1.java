package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__1;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireQuestion;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__1.class,AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__1.class,AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__1.class,AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__1.class},
    imports = {}
)
public interface AeaQuestionnaireQuestionVoToAeaQuestionnaireQuestionMapper__1 extends BaseMapper<AeaQuestionnaireQuestionVo, AeaQuestionnaireQuestion> {
}
