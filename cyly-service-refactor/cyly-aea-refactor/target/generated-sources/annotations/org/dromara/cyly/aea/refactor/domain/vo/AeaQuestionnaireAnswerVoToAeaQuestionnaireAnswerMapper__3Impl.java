package org.dromara.cyly.aea.refactor.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireAnswer;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:49:10+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__3Impl implements AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__3 {

    @Override
    public AeaQuestionnaireAnswer convert(AeaQuestionnaireAnswerVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaQuestionnaireAnswer aeaQuestionnaireAnswer = new AeaQuestionnaireAnswer();

        aeaQuestionnaireAnswer.setCreateBy( arg0.getCreateBy() );
        aeaQuestionnaireAnswer.setCreateTime( arg0.getCreateTime() );
        aeaQuestionnaireAnswer.setUpdateBy( arg0.getUpdateBy() );
        aeaQuestionnaireAnswer.setUpdateTime( arg0.getUpdateTime() );
        aeaQuestionnaireAnswer.setId( arg0.getId() );
        aeaQuestionnaireAnswer.setQuestionId( arg0.getQuestionId() );
        aeaQuestionnaireAnswer.setScore( arg0.getScore() );
        aeaQuestionnaireAnswer.setSort( arg0.getSort() );
        aeaQuestionnaireAnswer.setTitle( arg0.getTitle() );
        aeaQuestionnaireAnswer.setType( arg0.getType() );

        return aeaQuestionnaireAnswer;
    }

    @Override
    public AeaQuestionnaireAnswer convert(AeaQuestionnaireAnswerVo arg0, AeaQuestionnaireAnswer arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setId( arg0.getId() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setScore( arg0.getScore() );
        arg1.setSort( arg0.getSort() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setType( arg0.getType() );

        return arg1;
    }
}
