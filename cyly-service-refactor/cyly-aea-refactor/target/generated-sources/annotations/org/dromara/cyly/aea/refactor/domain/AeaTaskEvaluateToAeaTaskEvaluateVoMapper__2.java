package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaTaskEvaluateBoToAeaTaskEvaluateMapper__2;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateVoToAeaTaskEvaluateMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaTaskEvaluateBoToAeaTaskEvaluateMapper__2.class,AeaTaskEvaluateVoToAeaTaskEvaluateMapper__2.class},
    imports = {}
)
public interface AeaTaskEvaluateToAeaTaskEvaluateVoMapper__2 extends BaseMapper<AeaTaskEvaluate, AeaTaskEvaluateVo> {
}
