package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireAnswer;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__3.class,AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__3.class},
    imports = {}
)
public interface AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__3 extends BaseMapper<AeaQuestionnaireAnswerVo, AeaQuestionnaireAnswer> {
}
