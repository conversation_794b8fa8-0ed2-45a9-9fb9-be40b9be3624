package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaElderRelationshipBoToAeaElderRelationshipMapper__1;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderRelationshipVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderRelationshipVoToAeaElderRelationshipMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaElderRelationshipVoToAeaElderRelationshipMapper__1.class,AeaElderRelationshipBoToAeaElderRelationshipMapper__1.class},
    imports = {}
)
public interface AeaElderRelationshipToAeaElderRelationshipVoMapper__1 extends BaseMapper<AeaElderRelationship, AeaElderRelationshipVo> {
}
