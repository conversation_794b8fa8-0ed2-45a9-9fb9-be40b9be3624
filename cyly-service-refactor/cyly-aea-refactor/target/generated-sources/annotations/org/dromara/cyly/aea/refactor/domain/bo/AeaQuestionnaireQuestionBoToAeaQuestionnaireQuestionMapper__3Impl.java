package org.dromara.cyly.aea.refactor.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireQuestion;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:49:08+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaQuestionnaireQuestionBoToAeaQuestionnaireQuestionMapper__3Impl implements AeaQuestionnaireQuestionBoToAeaQuestionnaireQuestionMapper__3 {

    @Override
    public AeaQuestionnaireQuestion convert(AeaQuestionnaireQuestionBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaQuestionnaireQuestion aeaQuestionnaireQuestion = new AeaQuestionnaireQuestion();

        aeaQuestionnaireQuestion.setCreateBy( arg0.getCreateBy() );
        aeaQuestionnaireQuestion.setCreateDept( arg0.getCreateDept() );
        aeaQuestionnaireQuestion.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaQuestionnaireQuestion.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaQuestionnaireQuestion.setSearchValue( arg0.getSearchValue() );
        aeaQuestionnaireQuestion.setUpdateBy( arg0.getUpdateBy() );
        aeaQuestionnaireQuestion.setUpdateTime( arg0.getUpdateTime() );
        aeaQuestionnaireQuestion.setAnswerType( arg0.getAnswerType() );
        aeaQuestionnaireQuestion.setContent( arg0.getContent() );
        aeaQuestionnaireQuestion.setId( arg0.getId() );
        aeaQuestionnaireQuestion.setIsRequired( arg0.getIsRequired() );
        aeaQuestionnaireQuestion.setParentId( arg0.getParentId() );
        aeaQuestionnaireQuestion.setQuestionnaireId( arg0.getQuestionnaireId() );
        aeaQuestionnaireQuestion.setSort( arg0.getSort() );

        return aeaQuestionnaireQuestion;
    }

    @Override
    public AeaQuestionnaireQuestion convert(AeaQuestionnaireQuestionBo arg0, AeaQuestionnaireQuestion arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAnswerType( arg0.getAnswerType() );
        arg1.setContent( arg0.getContent() );
        arg1.setId( arg0.getId() );
        arg1.setIsRequired( arg0.getIsRequired() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setQuestionnaireId( arg0.getQuestionnaireId() );
        arg1.setSort( arg0.getSort() );

        return arg1;
    }
}
