package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaTaskEvaluateInfoBoToAeaTaskEvaluateInfoMapper__1;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__1.class,AeaTaskEvaluateInfoBoToAeaTaskEvaluateInfoMapper__1.class},
    imports = {}
)
public interface AeaTaskEvaluateInfoToAeaTaskEvaluateInfoVoMapper__1 extends BaseMapper<AeaTaskEvaluateInfo, AeaTaskEvaluateInfoVo> {
}
