package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaTaskEvaluateBoToAeaTaskEvaluateMapper__4;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateVoToAeaTaskEvaluateMapper__4;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaTaskEvaluateBoToAeaTaskEvaluateMapper__4.class,AeaTaskEvaluateVoToAeaTaskEvaluateMapper__4.class},
    imports = {}
)
public interface AeaTaskEvaluateToAeaTaskEvaluateVoMapper__4 extends BaseMapper<AeaTaskEvaluate, AeaTaskEvaluateVo> {
}
