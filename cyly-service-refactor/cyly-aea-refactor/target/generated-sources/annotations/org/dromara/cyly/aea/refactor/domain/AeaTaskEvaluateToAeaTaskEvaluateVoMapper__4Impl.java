package org.dromara.cyly.aea.refactor.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:49:10+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskEvaluateToAeaTaskEvaluateVoMapper__4Impl implements AeaTaskEvaluateToAeaTaskEvaluateVoMapper__4 {

    @Override
    public AeaTaskEvaluateVo convert(AeaTaskEvaluate arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTaskEvaluateVo aeaTaskEvaluateVo = new AeaTaskEvaluateVo();

        aeaTaskEvaluateVo.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        aeaTaskEvaluateVo.setAssignDeptTime( arg0.getAssignDeptTime() );
        aeaTaskEvaluateVo.setAssignSource( arg0.getAssignSource() );
        aeaTaskEvaluateVo.setAssignTime( arg0.getAssignTime() );
        aeaTaskEvaluateVo.setAssignUserId( arg0.getAssignUserId() );
        if ( arg0.getAuditStatus() != null ) {
            aeaTaskEvaluateVo.setAuditStatus( arg0.getAuditStatus().longValue() );
        }
        aeaTaskEvaluateVo.setCreateBy( arg0.getCreateBy() );
        aeaTaskEvaluateVo.setCreateDept( arg0.getCreateDept() );
        aeaTaskEvaluateVo.setCreateTime( arg0.getCreateTime() );
        aeaTaskEvaluateVo.setDeptId( arg0.getDeptId() );
        aeaTaskEvaluateVo.setElderId( arg0.getElderId() );
        aeaTaskEvaluateVo.setEndTime( arg0.getEndTime() );
        aeaTaskEvaluateVo.setExpectedEndTime( arg0.getExpectedEndTime() );
        aeaTaskEvaluateVo.setExpectedStartTime( arg0.getExpectedStartTime() );
        aeaTaskEvaluateVo.setId( arg0.getId() );
        aeaTaskEvaluateVo.setPlanEndTime( arg0.getPlanEndTime() );
        aeaTaskEvaluateVo.setReasonCode( arg0.getReasonCode() );
        aeaTaskEvaluateVo.setRemark( arg0.getRemark() );
        aeaTaskEvaluateVo.setSchemeCode( arg0.getSchemeCode() );
        aeaTaskEvaluateVo.setSource( arg0.getSource() );
        aeaTaskEvaluateVo.setStartLocation( arg0.getStartLocation() );
        aeaTaskEvaluateVo.setStartMarkUrl( arg0.getStartMarkUrl() );
        aeaTaskEvaluateVo.setStartTime( arg0.getStartTime() );
        aeaTaskEvaluateVo.setStatus( arg0.getStatus() );
        aeaTaskEvaluateVo.setTaskNum( arg0.getTaskNum() );
        aeaTaskEvaluateVo.setUpdateBy( arg0.getUpdateBy() );
        aeaTaskEvaluateVo.setUpdateTime( arg0.getUpdateTime() );
        aeaTaskEvaluateVo.setViceEvaluateUserId( arg0.getViceEvaluateUserId() );

        return aeaTaskEvaluateVo;
    }

    @Override
    public AeaTaskEvaluateVo convert(AeaTaskEvaluate arg0, AeaTaskEvaluateVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAssessorEvaluateUserId( arg0.getAssessorEvaluateUserId() );
        arg1.setAssignDeptTime( arg0.getAssignDeptTime() );
        arg1.setAssignSource( arg0.getAssignSource() );
        arg1.setAssignTime( arg0.getAssignTime() );
        arg1.setAssignUserId( arg0.getAssignUserId() );
        if ( arg0.getAuditStatus() != null ) {
            arg1.setAuditStatus( arg0.getAuditStatus().longValue() );
        }
        else {
            arg1.setAuditStatus( null );
        }
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setElderId( arg0.getElderId() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setExpectedEndTime( arg0.getExpectedEndTime() );
        arg1.setExpectedStartTime( arg0.getExpectedStartTime() );
        arg1.setId( arg0.getId() );
        arg1.setPlanEndTime( arg0.getPlanEndTime() );
        arg1.setReasonCode( arg0.getReasonCode() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSchemeCode( arg0.getSchemeCode() );
        arg1.setSource( arg0.getSource() );
        arg1.setStartLocation( arg0.getStartLocation() );
        arg1.setStartMarkUrl( arg0.getStartMarkUrl() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTaskNum( arg0.getTaskNum() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setViceEvaluateUserId( arg0.getViceEvaluateUserId() );

        return arg1;
    }
}
