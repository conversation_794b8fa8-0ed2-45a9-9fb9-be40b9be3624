package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaTaskEvaluateInfoBoToAeaTaskEvaluateInfoMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__3.class,AeaTaskEvaluateInfoBoToAeaTaskEvaluateInfoMapper__3.class},
    imports = {}
)
public interface AeaTaskEvaluateInfoToAeaTaskEvaluateInfoVoMapper__3 extends BaseMapper<AeaTaskEvaluateInfo, AeaTaskEvaluateInfoVo> {
}
