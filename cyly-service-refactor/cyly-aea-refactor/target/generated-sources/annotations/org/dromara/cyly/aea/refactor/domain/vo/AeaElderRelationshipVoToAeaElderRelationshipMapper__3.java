package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaElderRelationship;
import org.dromara.cyly.aea.refactor.domain.AeaElderRelationshipToAeaElderRelationshipVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaElderRelationshipToAeaElderRelationshipVoMapper__3.class},
    imports = {}
)
public interface AeaElderRelationshipVoToAeaElderRelationshipMapper__3 extends BaseMapper<AeaElderRelationshipVo, AeaElderRelationship> {
}
