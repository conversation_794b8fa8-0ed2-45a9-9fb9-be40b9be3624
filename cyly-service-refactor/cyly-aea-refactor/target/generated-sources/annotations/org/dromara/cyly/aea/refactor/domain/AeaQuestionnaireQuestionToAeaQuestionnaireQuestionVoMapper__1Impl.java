package org.dromara.cyly.aea.refactor.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireQuestionVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:40:36+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__1Impl implements AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__1 {

    @Override
    public AeaQuestionnaireQuestionVo convert(AeaQuestionnaireQuestion arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaQuestionnaireQuestionVo aeaQuestionnaireQuestionVo = new AeaQuestionnaireQuestionVo();

        aeaQuestionnaireQuestionVo.setAnswerType( arg0.getAnswerType() );
        aeaQuestionnaireQuestionVo.setContent( arg0.getContent() );
        aeaQuestionnaireQuestionVo.setCreateBy( arg0.getCreateBy() );
        aeaQuestionnaireQuestionVo.setCreateTime( arg0.getCreateTime() );
        aeaQuestionnaireQuestionVo.setId( arg0.getId() );
        aeaQuestionnaireQuestionVo.setIsRequired( arg0.getIsRequired() );
        aeaQuestionnaireQuestionVo.setParentId( arg0.getParentId() );
        aeaQuestionnaireQuestionVo.setQuestionnaireId( arg0.getQuestionnaireId() );
        aeaQuestionnaireQuestionVo.setSort( arg0.getSort() );
        aeaQuestionnaireQuestionVo.setUpdateBy( arg0.getUpdateBy() );
        aeaQuestionnaireQuestionVo.setUpdateTime( arg0.getUpdateTime() );

        return aeaQuestionnaireQuestionVo;
    }

    @Override
    public AeaQuestionnaireQuestionVo convert(AeaQuestionnaireQuestion arg0, AeaQuestionnaireQuestionVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAnswerType( arg0.getAnswerType() );
        arg1.setContent( arg0.getContent() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setId( arg0.getId() );
        arg1.setIsRequired( arg0.getIsRequired() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setQuestionnaireId( arg0.getQuestionnaireId() );
        arg1.setSort( arg0.getSort() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
