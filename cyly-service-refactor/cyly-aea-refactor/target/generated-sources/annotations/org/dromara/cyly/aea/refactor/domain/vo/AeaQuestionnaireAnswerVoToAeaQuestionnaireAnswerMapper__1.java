package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireAnswer;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__1.class,AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__1.class},
    imports = {}
)
public interface AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__1 extends BaseMapper<AeaQuestionnaireAnswerVo, AeaQuestionnaireAnswer> {
}
