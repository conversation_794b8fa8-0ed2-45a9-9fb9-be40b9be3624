package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaGbMedicineBoToAeaGbMedicineMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaGbMedicineVoToAeaGbMedicineMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaGbMedicineVoToAeaGbMedicineMapper__3.class,AeaGbMedicineBoToAeaGbMedicineMapper__3.class},
    imports = {}
)
public interface AeaGbMedicineToAeaGbMedicineVoMapper__3 extends BaseMapper<AeaGbMedicine, AeaGbMedicineVo> {
}
