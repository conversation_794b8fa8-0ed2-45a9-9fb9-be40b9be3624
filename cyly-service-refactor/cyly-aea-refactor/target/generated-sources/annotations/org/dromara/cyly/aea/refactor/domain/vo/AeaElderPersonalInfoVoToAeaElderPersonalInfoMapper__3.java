package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaElderPersonalInfo;
import org.dromara.cyly.aea.refactor.domain.AeaElderPersonalInfoToAeaElderPersonalInfoVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaElderPersonalInfoToAeaElderPersonalInfoVoMapper__3.class},
    imports = {}
)
public interface AeaElderPersonalInfoVoToAeaElderPersonalInfoMapper__3 extends BaseMapper<AeaElderPersonalInfoVo, AeaElderPersonalInfo> {
}
