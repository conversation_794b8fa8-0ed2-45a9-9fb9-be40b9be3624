package org.dromara.cyly.aea.refactor.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateExecutionRecord;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:40:36+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskEvaluateExecutionRecordBoToAeaTaskEvaluateExecutionRecordMapper__1Impl implements AeaTaskEvaluateExecutionRecordBoToAeaTaskEvaluateExecutionRecordMapper__1 {

    @Override
    public AeaTaskEvaluateExecutionRecord convert(AeaTaskEvaluateExecutionRecordBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTaskEvaluateExecutionRecord aeaTaskEvaluateExecutionRecord = new AeaTaskEvaluateExecutionRecord();

        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaTaskEvaluateExecutionRecord.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaTaskEvaluateExecutionRecord.setSearchValue( arg0.getSearchValue() );
        aeaTaskEvaluateExecutionRecord.setAnswerId( arg0.getAnswerId() );
        aeaTaskEvaluateExecutionRecord.setBatchNum( arg0.getBatchNum() );
        aeaTaskEvaluateExecutionRecord.setContent( arg0.getContent() );
        aeaTaskEvaluateExecutionRecord.setCreateBy( arg0.getCreateBy() );
        aeaTaskEvaluateExecutionRecord.setCreateDept( arg0.getCreateDept() );
        aeaTaskEvaluateExecutionRecord.setCreateTime( arg0.getCreateTime() );
        aeaTaskEvaluateExecutionRecord.setDelFlag( arg0.getDelFlag() );
        aeaTaskEvaluateExecutionRecord.setId( arg0.getId() );
        aeaTaskEvaluateExecutionRecord.setInfoId( arg0.getInfoId() );
        aeaTaskEvaluateExecutionRecord.setQuestionId( arg0.getQuestionId() );
        aeaTaskEvaluateExecutionRecord.setScore( arg0.getScore() );
        aeaTaskEvaluateExecutionRecord.setTaskId( arg0.getTaskId() );
        aeaTaskEvaluateExecutionRecord.setUpdateBy( arg0.getUpdateBy() );
        aeaTaskEvaluateExecutionRecord.setUpdateTime( arg0.getUpdateTime() );

        return aeaTaskEvaluateExecutionRecord;
    }

    @Override
    public AeaTaskEvaluateExecutionRecord convert(AeaTaskEvaluateExecutionRecordBo arg0, AeaTaskEvaluateExecutionRecord arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setAnswerId( arg0.getAnswerId() );
        arg1.setBatchNum( arg0.getBatchNum() );
        arg1.setContent( arg0.getContent() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDelFlag( arg0.getDelFlag() );
        arg1.setId( arg0.getId() );
        arg1.setInfoId( arg0.getInfoId() );
        arg1.setQuestionId( arg0.getQuestionId() );
        arg1.setScore( arg0.getScore() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
