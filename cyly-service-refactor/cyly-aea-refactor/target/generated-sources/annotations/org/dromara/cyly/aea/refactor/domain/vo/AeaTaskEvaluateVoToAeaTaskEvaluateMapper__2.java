package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluate;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateToAeaTaskEvaluateVoMapper__2;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaTaskEvaluateToAeaTaskEvaluateVoMapper__2.class},
    imports = {}
)
public interface AeaTaskEvaluateVoToAeaTaskEvaluateMapper__2 extends BaseMapper<AeaTaskEvaluateVo, AeaTaskEvaluate> {
}
