package org.dromara.cyly.aea.refactor.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaElderRelationship;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:49:09+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaElderRelationshipBoToAeaElderRelationshipMapper__3Impl implements AeaElderRelationshipBoToAeaElderRelationshipMapper__3 {

    @Override
    public AeaElderRelationship convert(AeaElderRelationshipBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaElderRelationship aeaElderRelationship = new AeaElderRelationship();

        aeaElderRelationship.setCreateDept( arg0.getCreateDept() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaElderRelationship.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaElderRelationship.setSearchValue( arg0.getSearchValue() );
        aeaElderRelationship.setAddressDetail( arg0.getAddressDetail() );
        aeaElderRelationship.setAeaElderId( arg0.getAeaElderId() );
        aeaElderRelationship.setCreateBy( arg0.getCreateBy() );
        aeaElderRelationship.setCreateTime( arg0.getCreateTime() );
        aeaElderRelationship.setGender( arg0.getGender() );
        aeaElderRelationship.setId( arg0.getId() );
        aeaElderRelationship.setIdCard( arg0.getIdCard() );
        aeaElderRelationship.setIsDel( arg0.getIsDel() );
        aeaElderRelationship.setIsEmergencyContact( arg0.getIsEmergencyContact() );
        aeaElderRelationship.setIsTogether( arg0.getIsTogether() );
        aeaElderRelationship.setMobilePhone( arg0.getMobilePhone() );
        aeaElderRelationship.setName( arg0.getName() );
        aeaElderRelationship.setRelationshipType( arg0.getRelationshipType() );
        aeaElderRelationship.setUpdateBy( arg0.getUpdateBy() );
        aeaElderRelationship.setUpdateTime( arg0.getUpdateTime() );

        return aeaElderRelationship;
    }

    @Override
    public AeaElderRelationship convert(AeaElderRelationshipBo arg0, AeaElderRelationship arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateDept( arg0.getCreateDept() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setAddressDetail( arg0.getAddressDetail() );
        arg1.setAeaElderId( arg0.getAeaElderId() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setGender( arg0.getGender() );
        arg1.setId( arg0.getId() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setIsEmergencyContact( arg0.getIsEmergencyContact() );
        arg1.setIsTogether( arg0.getIsTogether() );
        arg1.setMobilePhone( arg0.getMobilePhone() );
        arg1.setName( arg0.getName() );
        arg1.setRelationshipType( arg0.getRelationshipType() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
