package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaTaskEvaluateExecutionRecordBoToAeaTaskEvaluateExecutionRecordMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateExecutionRecordVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateExecutionRecordVoToAeaTaskEvaluateExecutionRecordMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaTaskEvaluateExecutionRecordBoToAeaTaskEvaluateExecutionRecordMapper__3.class,AeaTaskEvaluateExecutionRecordVoToAeaTaskEvaluateExecutionRecordMapper__3.class},
    imports = {}
)
public interface AeaTaskEvaluateExecutionRecordToAeaTaskEvaluateExecutionRecordVoMapper__3 extends BaseMapper<AeaTaskEvaluateExecutionRecord, AeaTaskEvaluateExecutionRecordVo> {
}
