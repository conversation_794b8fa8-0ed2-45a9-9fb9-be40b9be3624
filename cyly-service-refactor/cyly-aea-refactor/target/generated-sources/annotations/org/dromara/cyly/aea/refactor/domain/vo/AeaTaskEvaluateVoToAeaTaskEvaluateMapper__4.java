package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluate;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateToAeaTaskEvaluateVoMapper__4;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaTaskEvaluateToAeaTaskEvaluateVoMapper__4.class},
    imports = {}
)
public interface AeaTaskEvaluateVoToAeaTaskEvaluateMapper__4 extends BaseMapper<AeaTaskEvaluateVo, AeaTaskEvaluate> {
}
