package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaEvaluateReportBoToAeaEvaluateReportMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVoToAeaEvaluateReportMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaEvaluateReportVoToAeaEvaluateReportMapper__3.class,AeaEvaluateReportBoToAeaEvaluateReportMapper__3.class},
    imports = {}
)
public interface AeaEvaluateReportToAeaEvaluateReportVoMapper__3 extends BaseMapper<AeaEvaluateReport, AeaEvaluateReportVo> {
}
