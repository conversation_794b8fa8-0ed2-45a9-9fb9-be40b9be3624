package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateExecutionRecord;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateExecutionRecordToAeaTaskEvaluateExecutionRecordVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaTaskEvaluateExecutionRecordToAeaTaskEvaluateExecutionRecordVoMapper__1.class},
    imports = {}
)
public interface AeaTaskEvaluateExecutionRecordVoToAeaTaskEvaluateExecutionRecordMapper__1 extends BaseMapper<AeaTaskEvaluateExecutionRecordVo, AeaTaskEvaluateExecutionRecord> {
}
