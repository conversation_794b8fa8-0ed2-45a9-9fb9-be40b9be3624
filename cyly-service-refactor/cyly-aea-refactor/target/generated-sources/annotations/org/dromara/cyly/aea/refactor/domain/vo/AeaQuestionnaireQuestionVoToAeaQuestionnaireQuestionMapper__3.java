package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__3;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireQuestion;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__3.class,AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__3.class,AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__3.class,AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__3.class},
    imports = {}
)
public interface AeaQuestionnaireQuestionVoToAeaQuestionnaireQuestionMapper__3 extends BaseMapper<AeaQuestionnaireQuestionVo, AeaQuestionnaireQuestion> {
}
