package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaEvaluateReportBoToAeaEvaluateReportMapper__1;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVoToAeaEvaluateReportMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaEvaluateReportVoToAeaEvaluateReportMapper__1.class,AeaEvaluateReportBoToAeaEvaluateReportMapper__1.class},
    imports = {}
)
public interface AeaEvaluateReportToAeaEvaluateReportVoMapper__1 extends BaseMapper<AeaEvaluateReport, AeaEvaluateReportVo> {
}
