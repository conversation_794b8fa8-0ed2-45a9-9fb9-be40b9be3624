package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaQuestionnaireQuestionBoToAeaQuestionnaireQuestionMapper__1;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__1;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireQuestionVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireQuestionVoToAeaQuestionnaireQuestionMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__1.class,AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__1.class,AeaQuestionnaireQuestionVoToAeaQuestionnaireQuestionMapper__1.class,AeaQuestionnaireQuestionBoToAeaQuestionnaireQuestionMapper__1.class},
    imports = {}
)
public interface AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__1 extends BaseMapper<AeaQuestionnaireQuestion, AeaQuestionnaireQuestionVo> {
}
