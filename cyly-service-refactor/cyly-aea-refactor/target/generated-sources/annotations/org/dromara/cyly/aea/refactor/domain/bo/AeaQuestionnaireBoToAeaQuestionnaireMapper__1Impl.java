package org.dromara.cyly.aea.refactor.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaire;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:40:37+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaQuestionnaireBoToAeaQuestionnaireMapper__1Impl implements AeaQuestionnaireBoToAeaQuestionnaireMapper__1 {

    @Override
    public AeaQuestionnaire convert(AeaQuestionnaireBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaQuestionnaire aeaQuestionnaire = new AeaQuestionnaire();

        aeaQuestionnaire.setCreateBy( arg0.getCreateBy() );
        aeaQuestionnaire.setCreateDept( arg0.getCreateDept() );
        aeaQuestionnaire.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaQuestionnaire.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaQuestionnaire.setSearchValue( arg0.getSearchValue() );
        aeaQuestionnaire.setUpdateBy( arg0.getUpdateBy() );
        aeaQuestionnaire.setUpdateTime( arg0.getUpdateTime() );
        aeaQuestionnaire.setId( arg0.getId() );
        aeaQuestionnaire.setParentId( arg0.getParentId() );
        aeaQuestionnaire.setRoute( arg0.getRoute() );
        aeaQuestionnaire.setShowType( arg0.getShowType() );
        aeaQuestionnaire.setSort( arg0.getSort() );
        aeaQuestionnaire.setStatus( arg0.getStatus() );
        aeaQuestionnaire.setTitle( arg0.getTitle() );
        aeaQuestionnaire.setType( arg0.getType() );

        return aeaQuestionnaire;
    }

    @Override
    public AeaQuestionnaire convert(AeaQuestionnaireBo arg0, AeaQuestionnaire arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setId( arg0.getId() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setRoute( arg0.getRoute() );
        arg1.setShowType( arg0.getShowType() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setType( arg0.getType() );

        return arg1;
    }
}
