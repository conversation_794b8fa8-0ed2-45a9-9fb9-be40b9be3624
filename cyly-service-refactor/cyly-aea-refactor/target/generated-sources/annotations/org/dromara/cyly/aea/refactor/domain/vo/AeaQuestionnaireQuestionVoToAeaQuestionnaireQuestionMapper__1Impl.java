package org.dromara.cyly.aea.refactor.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireQuestion;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:40:37+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaQuestionnaireQuestionVoToAeaQuestionnaireQuestionMapper__1Impl implements AeaQuestionnaireQuestionVoToAeaQuestionnaireQuestionMapper__1 {

    @Override
    public AeaQuestionnaireQuestion convert(AeaQuestionnaireQuestionVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaQuestionnaireQuestion aeaQuestionnaireQuestion = new AeaQuestionnaireQuestion();

        aeaQuestionnaireQuestion.setCreateBy( arg0.getCreateBy() );
        aeaQuestionnaireQuestion.setCreateTime( arg0.getCreateTime() );
        aeaQuestionnaireQuestion.setUpdateBy( arg0.getUpdateBy() );
        aeaQuestionnaireQuestion.setUpdateTime( arg0.getUpdateTime() );
        aeaQuestionnaireQuestion.setAnswerType( arg0.getAnswerType() );
        aeaQuestionnaireQuestion.setContent( arg0.getContent() );
        aeaQuestionnaireQuestion.setId( arg0.getId() );
        aeaQuestionnaireQuestion.setIsRequired( arg0.getIsRequired() );
        aeaQuestionnaireQuestion.setParentId( arg0.getParentId() );
        aeaQuestionnaireQuestion.setQuestionnaireId( arg0.getQuestionnaireId() );
        aeaQuestionnaireQuestion.setSort( arg0.getSort() );

        return aeaQuestionnaireQuestion;
    }

    @Override
    public AeaQuestionnaireQuestion convert(AeaQuestionnaireQuestionVo arg0, AeaQuestionnaireQuestion arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAnswerType( arg0.getAnswerType() );
        arg1.setContent( arg0.getContent() );
        arg1.setId( arg0.getId() );
        arg1.setIsRequired( arg0.getIsRequired() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setQuestionnaireId( arg0.getQuestionnaireId() );
        arg1.setSort( arg0.getSort() );

        return arg1;
    }
}
