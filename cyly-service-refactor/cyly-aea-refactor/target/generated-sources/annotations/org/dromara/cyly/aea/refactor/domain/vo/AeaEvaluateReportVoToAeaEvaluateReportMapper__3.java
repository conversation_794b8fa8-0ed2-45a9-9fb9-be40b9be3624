package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReport;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReportToAeaEvaluateReportVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaEvaluateReportToAeaEvaluateReportVoMapper__3.class},
    imports = {}
)
public interface AeaEvaluateReportVoToAeaEvaluateReportMapper__3 extends BaseMapper<AeaEvaluateReportVo, AeaEvaluateReport> {
}
