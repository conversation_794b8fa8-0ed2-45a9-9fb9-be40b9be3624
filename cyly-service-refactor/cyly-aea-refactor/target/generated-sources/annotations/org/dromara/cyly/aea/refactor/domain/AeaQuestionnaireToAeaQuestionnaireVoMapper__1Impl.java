package org.dromara.cyly.aea.refactor.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:40:37+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaQuestionnaireToAeaQuestionnaireVoMapper__1Impl implements AeaQuestionnaireToAeaQuestionnaireVoMapper__1 {

    @Override
    public AeaQuestionnaireVo convert(AeaQuestionnaire arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaQuestionnaireVo aeaQuestionnaireVo = new AeaQuestionnaireVo();

        aeaQuestionnaireVo.setCreateBy( arg0.getCreateBy() );
        aeaQuestionnaireVo.setCreateTime( arg0.getCreateTime() );
        aeaQuestionnaireVo.setId( arg0.getId() );
        aeaQuestionnaireVo.setParentId( arg0.getParentId() );
        aeaQuestionnaireVo.setRoute( arg0.getRoute() );
        aeaQuestionnaireVo.setShowType( arg0.getShowType() );
        aeaQuestionnaireVo.setSort( arg0.getSort() );
        aeaQuestionnaireVo.setStatus( arg0.getStatus() );
        aeaQuestionnaireVo.setTitle( arg0.getTitle() );
        aeaQuestionnaireVo.setType( arg0.getType() );
        aeaQuestionnaireVo.setUpdateBy( arg0.getUpdateBy() );
        aeaQuestionnaireVo.setUpdateTime( arg0.getUpdateTime() );

        return aeaQuestionnaireVo;
    }

    @Override
    public AeaQuestionnaireVo convert(AeaQuestionnaire arg0, AeaQuestionnaireVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setId( arg0.getId() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setRoute( arg0.getRoute() );
        arg1.setShowType( arg0.getShowType() );
        arg1.setSort( arg0.getSort() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTitle( arg0.getTitle() );
        arg1.setType( arg0.getType() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
