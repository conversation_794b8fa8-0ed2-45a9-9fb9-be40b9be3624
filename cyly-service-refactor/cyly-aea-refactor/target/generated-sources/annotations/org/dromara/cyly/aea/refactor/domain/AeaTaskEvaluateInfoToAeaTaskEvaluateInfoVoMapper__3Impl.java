package org.dromara.cyly.aea.refactor.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:49:09+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskEvaluateInfoToAeaTaskEvaluateInfoVoMapper__3Impl implements AeaTaskEvaluateInfoToAeaTaskEvaluateInfoVoMapper__3 {

    @Override
    public AeaTaskEvaluateInfoVo convert(AeaTaskEvaluateInfo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTaskEvaluateInfoVo aeaTaskEvaluateInfoVo = new AeaTaskEvaluateInfoVo();

        aeaTaskEvaluateInfoVo.setCreateBy( arg0.getCreateBy() );
        aeaTaskEvaluateInfoVo.setCreateDept( arg0.getCreateDept() );
        aeaTaskEvaluateInfoVo.setCreateTime( arg0.getCreateTime() );
        aeaTaskEvaluateInfoVo.setEndTime( arg0.getEndTime() );
        aeaTaskEvaluateInfoVo.setEvaluateUserId( arg0.getEvaluateUserId() );
        aeaTaskEvaluateInfoVo.setId( arg0.getId() );
        aeaTaskEvaluateInfoVo.setQuestionnaireId( arg0.getQuestionnaireId() );
        aeaTaskEvaluateInfoVo.setRemark( arg0.getRemark() );
        aeaTaskEvaluateInfoVo.setStartTime( arg0.getStartTime() );
        aeaTaskEvaluateInfoVo.setStatus( arg0.getStatus() );
        aeaTaskEvaluateInfoVo.setTaskId( arg0.getTaskId() );
        aeaTaskEvaluateInfoVo.setTotalScore( arg0.getTotalScore() );
        aeaTaskEvaluateInfoVo.setUpdateBy( arg0.getUpdateBy() );
        aeaTaskEvaluateInfoVo.setUpdateTime( arg0.getUpdateTime() );

        return aeaTaskEvaluateInfoVo;
    }

    @Override
    public AeaTaskEvaluateInfoVo convert(AeaTaskEvaluateInfo arg0, AeaTaskEvaluateInfoVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setEvaluateUserId( arg0.getEvaluateUserId() );
        arg1.setId( arg0.getId() );
        arg1.setQuestionnaireId( arg0.getQuestionnaireId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setTotalScore( arg0.getTotalScore() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
