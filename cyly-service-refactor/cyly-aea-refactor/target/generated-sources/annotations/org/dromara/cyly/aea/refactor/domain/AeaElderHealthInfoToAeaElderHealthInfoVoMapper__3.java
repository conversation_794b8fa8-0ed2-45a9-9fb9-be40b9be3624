package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaElderHealthInfoBoToAeaElderHealthInfoMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderHealthInfoVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderHealthInfoVoToAeaElderHealthInfoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaElderHealthInfoVoToAeaElderHealthInfoMapper__3.class,AeaElderHealthInfoBoToAeaElderHealthInfoMapper__3.class},
    imports = {}
)
public interface AeaElderHealthInfoToAeaElderHealthInfoVoMapper__3 extends BaseMapper<AeaElderHealthInfo, AeaElderHealthInfoVo> {
}
