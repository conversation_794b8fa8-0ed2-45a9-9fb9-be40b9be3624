package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaQuestionnaireAnswerBoToAeaQuestionnaireAnswerMapper__1;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireAnswerVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaQuestionnaireAnswerBoToAeaQuestionnaireAnswerMapper__1.class,AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__1.class},
    imports = {}
)
public interface AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__1 extends BaseMapper<AeaQuestionnaireAnswer, AeaQuestionnaireAnswerVo> {
}
