package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaElderRelationshipBoToAeaElderRelationshipMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderRelationshipVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderRelationshipVoToAeaElderRelationshipMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaElderRelationshipVoToAeaElderRelationshipMapper__3.class,AeaElderRelationshipBoToAeaElderRelationshipMapper__3.class},
    imports = {}
)
public interface AeaElderRelationshipToAeaElderRelationshipVoMapper__3 extends BaseMapper<AeaElderRelationship, AeaElderRelationshipVo> {
}
