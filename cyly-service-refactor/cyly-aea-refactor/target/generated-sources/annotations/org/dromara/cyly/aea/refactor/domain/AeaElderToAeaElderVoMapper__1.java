package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderVoToAeaElderMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaElderVoToAeaElderMapper__1.class},
    imports = {}
)
public interface AeaElderToAeaElderVoMapper__1 extends BaseMapper<AeaElder, AeaElderVo> {
}
