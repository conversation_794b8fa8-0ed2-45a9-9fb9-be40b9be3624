package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaElderHealthInfo;
import org.dromara.cyly.aea.refactor.domain.AeaElderHealthInfoToAeaElderHealthInfoVoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaElderHealthInfoToAeaElderHealthInfoVoMapper__3.class},
    imports = {}
)
public interface AeaElderHealthInfoVoToAeaElderHealthInfoMapper__3 extends BaseMapper<AeaElderHealthInfoVo, AeaElderHealthInfo> {
}
