package org.dromara.cyly.aea.refactor.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateInfo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:49:09+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__3Impl implements AeaTaskEvaluateInfoVoToAeaTaskEvaluateInfoMapper__3 {

    @Override
    public AeaTaskEvaluateInfo convert(AeaTaskEvaluateInfoVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaTaskEvaluateInfo aeaTaskEvaluateInfo = new AeaTaskEvaluateInfo();

        aeaTaskEvaluateInfo.setCreateBy( arg0.getCreateBy() );
        aeaTaskEvaluateInfo.setCreateDept( arg0.getCreateDept() );
        aeaTaskEvaluateInfo.setCreateTime( arg0.getCreateTime() );
        aeaTaskEvaluateInfo.setEndTime( arg0.getEndTime() );
        aeaTaskEvaluateInfo.setEvaluateUserId( arg0.getEvaluateUserId() );
        aeaTaskEvaluateInfo.setId( arg0.getId() );
        aeaTaskEvaluateInfo.setQuestionnaireId( arg0.getQuestionnaireId() );
        aeaTaskEvaluateInfo.setRemark( arg0.getRemark() );
        aeaTaskEvaluateInfo.setStartTime( arg0.getStartTime() );
        aeaTaskEvaluateInfo.setStatus( arg0.getStatus() );
        aeaTaskEvaluateInfo.setTaskId( arg0.getTaskId() );
        aeaTaskEvaluateInfo.setTotalScore( arg0.getTotalScore() );
        aeaTaskEvaluateInfo.setUpdateBy( arg0.getUpdateBy() );
        aeaTaskEvaluateInfo.setUpdateTime( arg0.getUpdateTime() );

        return aeaTaskEvaluateInfo;
    }

    @Override
    public AeaTaskEvaluateInfo convert(AeaTaskEvaluateInfoVo arg0, AeaTaskEvaluateInfo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setEndTime( arg0.getEndTime() );
        arg1.setEvaluateUserId( arg0.getEvaluateUserId() );
        arg1.setId( arg0.getId() );
        arg1.setQuestionnaireId( arg0.getQuestionnaireId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setStartTime( arg0.getStartTime() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setTotalScore( arg0.getTotalScore() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );

        return arg1;
    }
}
