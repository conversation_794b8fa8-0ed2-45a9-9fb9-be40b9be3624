package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaElder;
import org.dromara.cyly.aea.refactor.domain.AeaElderToAeaElderVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaElderToAeaElderVoMapper__1.class},
    imports = {}
)
public interface AeaElderVoToAeaElderMapper__1 extends BaseMapper<AeaElderVo, AeaElder> {
}
