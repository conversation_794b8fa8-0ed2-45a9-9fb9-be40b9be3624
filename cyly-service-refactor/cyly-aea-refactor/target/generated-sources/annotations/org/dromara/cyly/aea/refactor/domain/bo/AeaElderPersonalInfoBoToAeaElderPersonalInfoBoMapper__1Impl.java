package org.dromara.cyly.aea.refactor.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T17:40:37+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AeaElderPersonalInfoBoToAeaElderPersonalInfoBoMapper__1Impl implements AeaElderPersonalInfoBoToAeaElderPersonalInfoBoMapper__1 {

    @Override
    public AeaElderPersonalInfoBo convert(AeaElderPersonalInfoBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AeaElderPersonalInfoBo aeaElderPersonalInfoBo = new AeaElderPersonalInfoBo();

        aeaElderPersonalInfoBo.setCreateDept( arg0.getCreateDept() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            aeaElderPersonalInfoBo.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        aeaElderPersonalInfoBo.setSearchValue( arg0.getSearchValue() );
        aeaElderPersonalInfoBo.setAeaElderId( arg0.getAeaElderId() );
        aeaElderPersonalInfoBo.setChildrenNum( arg0.getChildrenNum() );
        aeaElderPersonalInfoBo.setCreateBy( arg0.getCreateBy() );
        aeaElderPersonalInfoBo.setCreateTime( arg0.getCreateTime() );
        aeaElderPersonalInfoBo.setEducationLevel( arg0.getEducationLevel() );
        aeaElderPersonalInfoBo.setHasReligion( arg0.getHasReligion() );
        aeaElderPersonalInfoBo.setHousingNature( arg0.getHousingNature() );
        aeaElderPersonalInfoBo.setIncomeSource( arg0.getIncomeSource() );
        aeaElderPersonalInfoBo.setIsDel( arg0.getIsDel() );
        aeaElderPersonalInfoBo.setKind( arg0.getKind() );
        aeaElderPersonalInfoBo.setMaritalCode( arg0.getMaritalCode() );
        aeaElderPersonalInfoBo.setMedicalType( arg0.getMedicalType() );
        aeaElderPersonalInfoBo.setOccupationCategory( arg0.getOccupationCategory() );
        aeaElderPersonalInfoBo.setPoliticalAffiliation( arg0.getPoliticalAffiliation() );
        aeaElderPersonalInfoBo.setReligiousBelief( arg0.getReligiousBelief() );
        aeaElderPersonalInfoBo.setResidency( arg0.getResidency() );
        aeaElderPersonalInfoBo.setSpecialTypes( arg0.getSpecialTypes() );
        aeaElderPersonalInfoBo.setUpdateBy( arg0.getUpdateBy() );
        aeaElderPersonalInfoBo.setUpdateTime( arg0.getUpdateTime() );
        aeaElderPersonalInfoBo.setYiNianCardNum( arg0.getYiNianCardNum() );

        return aeaElderPersonalInfoBo;
    }

    @Override
    public AeaElderPersonalInfoBo convert(AeaElderPersonalInfoBo arg0, AeaElderPersonalInfoBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateDept( arg0.getCreateDept() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setAeaElderId( arg0.getAeaElderId() );
        arg1.setChildrenNum( arg0.getChildrenNum() );
        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setEducationLevel( arg0.getEducationLevel() );
        arg1.setHasReligion( arg0.getHasReligion() );
        arg1.setHousingNature( arg0.getHousingNature() );
        arg1.setIncomeSource( arg0.getIncomeSource() );
        arg1.setIsDel( arg0.getIsDel() );
        arg1.setKind( arg0.getKind() );
        arg1.setMaritalCode( arg0.getMaritalCode() );
        arg1.setMedicalType( arg0.getMedicalType() );
        arg1.setOccupationCategory( arg0.getOccupationCategory() );
        arg1.setPoliticalAffiliation( arg0.getPoliticalAffiliation() );
        arg1.setReligiousBelief( arg0.getReligiousBelief() );
        arg1.setResidency( arg0.getResidency() );
        arg1.setSpecialTypes( arg0.getSpecialTypes() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setYiNianCardNum( arg0.getYiNianCardNum() );

        return arg1;
    }
}
