package org.dromara.cyly.aea.refactor.domain.vo;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaire;
import org.dromara.cyly.aea.refactor.domain.AeaQuestionnaireToAeaQuestionnaireVoMapper__3;
import org.mapstruct.Mapper;
import org.mapstruct.ReportingPolicy;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaQuestionnaireToAeaQuestionnaireVoMapper__3.class,AeaQuestionnaireToAeaQuestionnaireVoMapper__3.class},
    imports = {},
    unmappedTargetPolicy = ReportingPolicy.IGNORE
)
public interface AeaQuestionnaireVoToAeaQuestionnaireMapper__3 extends BaseMapper<AeaQuestionnaireVo, AeaQuestionnaire> {
}
