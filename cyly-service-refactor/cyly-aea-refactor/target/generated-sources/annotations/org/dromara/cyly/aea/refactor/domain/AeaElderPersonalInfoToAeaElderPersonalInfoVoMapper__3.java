package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderPersonalInfoVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaElderPersonalInfoVoToAeaElderPersonalInfoMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaElderPersonalInfoVoToAeaElderPersonalInfoMapper__3.class},
    imports = {}
)
public interface AeaElderPersonalInfoToAeaElderPersonalInfoVoMapper__3 extends BaseMapper<AeaElderPersonalInfo, AeaElderPersonalInfoVo> {
}
