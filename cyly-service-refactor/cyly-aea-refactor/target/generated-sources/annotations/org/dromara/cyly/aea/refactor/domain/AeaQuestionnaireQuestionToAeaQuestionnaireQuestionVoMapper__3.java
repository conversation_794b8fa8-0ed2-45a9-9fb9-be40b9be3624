package org.dromara.cyly.aea.refactor.domain;

import io.github.linpeilie.AutoMapperConfig__542;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.aea.refactor.domain.bo.AeaQuestionnaireQuestionBoToAeaQuestionnaireQuestionMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__3;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireQuestionVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireQuestionVoToAeaQuestionnaireQuestionMapper__3;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__542.class,
    uses = {AeaQuestionnaireAnswerVoToAeaQuestionnaireAnswerMapper__3.class,AeaQuestionnaireAnswerToAeaQuestionnaireAnswerVoMapper__3.class,AeaQuestionnaireQuestionVoToAeaQuestionnaireQuestionMapper__3.class,AeaQuestionnaireQuestionBoToAeaQuestionnaireQuestionMapper__3.class},
    imports = {}
)
public interface AeaQuestionnaireQuestionToAeaQuestionnaireQuestionVoMapper__3 extends BaseMapper<AeaQuestionnaireQuestion, AeaQuestionnaireQuestionVo> {
}
