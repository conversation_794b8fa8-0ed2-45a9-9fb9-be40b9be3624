/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 80024 (8.0.24)
 Source Host           : *************:3306
 Source Schema         : cyly-aea

 Target Server Type    : MySQL
 Target Server Version : 80024 (8.0.24)
 File Encoding         : 65001

 Date: 08/06/2025 19:13:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for aea_task_evaluate_execution_record
-- ----------------------------
DROP TABLE IF EXISTS `aea_task_evaluate_execution_record`;
CREATE TABLE `aea_task_evaluate_execution_record`  (
  `id` bigint NOT NULL COMMENT '主键',
  `info_id` bigint NOT NULL COMMENT '关联任务业务详情表id',
  `task_id` bigint NOT NULL COMMENT '关联任务记录表id',
  `question_id` bigint NOT NULL COMMENT '关联评估问卷问题表id',
  `answer_id` bigint NULL DEFAULT NULL COMMENT '关联评估问卷答案表id',
  `content` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '用户输入的文本内容（非选项答案时填写）',
  `score` int NULL DEFAULT NULL COMMENT '评分值',
  `batch_num` int NULL DEFAULT 0 COMMENT '批次号, 针对一个问题分成子问题, 并且可以多次作答是, 例如(用药填写, 需要写多种药物和服用方法等)',
  `del_flag` tinyint NULL DEFAULT 0 COMMENT '删除状态: \n        0-否, \n        1-是',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建机构',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者id（app_user）',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_info_id`(`info_id` ASC) USING BTREE,
  INDEX `idx_question_id`(`question_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评估任务执行表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of aea_task_evaluate_execution_record
-- ----------------------------
INSERT INTO `aea_task_evaluate_execution_record` VALUES (1931339924650577922, 1931322402400333826, 1931322380912914433, 1930097083944624129, 1930098836605853697, '', 3, 1, 0, NULL, NULL, '2025-06-07 21:18:15', NULL, NULL);
INSERT INTO `aea_task_evaluate_execution_record` VALUES (1931339924705103874, 1931322402400333826, 1931322380912914433, 1930097562212720642, 1930101337149235201, '', 1, 1, 0, NULL, NULL, '2025-06-07 21:18:15', NULL, NULL);
INSERT INTO `aea_task_evaluate_execution_record` VALUES (1931339924768018433, 1931322402400333826, 1931322380912914433, 1930097904585367553, 1930103551016132609, '', 2, 1, 0, NULL, NULL, '2025-06-07 21:18:15', NULL, NULL);
INSERT INTO `aea_task_evaluate_execution_record` VALUES (1931339924805767170, 1931322402400333826, 1931322380912914433, 1930098180553793537, 1930105166917885953, '', 0, 1, 0, NULL, NULL, '2025-06-07 21:18:15', NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
