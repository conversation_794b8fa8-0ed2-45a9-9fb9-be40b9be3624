/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 80024 (8.0.24)
 Source Host           : *************:3306
 Source Schema         : cyly-aea

 Target Server Type    : MySQL
 Target Server Version : 80024 (8.0.24)
 File Encoding         : 65001

 Date: 08/06/2025 19:13:15
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for aea_task_evaluate_info
-- ----------------------------
DROP TABLE IF EXISTS `aea_task_evaluate_info`;
CREATE TABLE `aea_task_evaluate_info`  (
  `id` bigint NOT NULL COMMENT '主键',
  `task_id` bigint NOT NULL COMMENT '关联任务记录表id',
  `questionnaire_id` bigint NOT NULL COMMENT '关联评估问卷表id',
  `evaluate_user_id` bigint NULL DEFAULT NULL COMMENT '执行评估的用户id（app_user）',
  `total_score` int NULL DEFAULT NULL COMMENT '提交得分总分',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '评估状态: \n        0-待开始, \n        1-执行中, \n        2-执行成功, \n        3-执行失败',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: \n        0-否, \n        1-是',
  `start_time` datetime NULL DEFAULT NULL COMMENT '评估开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '评估完成时间',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建部门',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '评估任务模块记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of aea_task_evaluate_info
-- ----------------------------
INSERT INTO `aea_task_evaluate_info` VALUES (1931322402224173057, 1931322380912914433, 1930096957461192706, NULL, 0, 0, NULL, 0, '2025-06-07 20:08:38', NULL, NULL, NULL, '2025-06-07 20:08:38', NULL, NULL);
INSERT INTO `aea_task_evaluate_info` VALUES (1931322402320642050, 1931322380912914433, 1930099855561678849, NULL, 0, 0, NULL, 0, '2025-06-07 20:08:38', NULL, NULL, NULL, '2025-06-07 20:08:38', NULL, NULL);
INSERT INTO `aea_task_evaluate_info` VALUES (1931322402400333826, 1931322380912914433, 1930095404272672769, NULL, 6, 2, NULL, 0, '2025-06-07 20:08:38', '2025-06-07 21:18:15', NULL, NULL, '2025-06-07 20:08:38', NULL, '2025-06-07 21:18:15');
INSERT INTO `aea_task_evaluate_info` VALUES (1931322402471636993, 1931322380912914433, 1930099989276090370, NULL, 0, 0, NULL, 0, '2025-06-07 20:08:38', NULL, NULL, NULL, '2025-06-07 20:08:38', NULL, NULL);
INSERT INTO `aea_task_evaluate_info` VALUES (1931322402542940161, 1931322380912914433, 1930096027466555394, NULL, 0, 0, NULL, 0, '2025-06-07 20:08:38', NULL, NULL, NULL, '2025-06-07 20:08:38', NULL, NULL);
INSERT INTO `aea_task_evaluate_info` VALUES (1931322402631020545, 1931322380912914433, 1930100095031271426, NULL, 0, 0, NULL, 0, '2025-06-07 20:08:38', NULL, NULL, NULL, '2025-06-07 20:08:38', NULL, NULL);
INSERT INTO `aea_task_evaluate_info` VALUES (1931322402714906626, 1931322380912914433, 1930100265798164482, NULL, 0, 0, NULL, 0, '2025-06-07 20:08:38', NULL, NULL, NULL, '2025-06-07 20:08:38', NULL, NULL);
INSERT INTO `aea_task_evaluate_info` VALUES (1931322402794598402, 1931322380912914433, 1930104438665076738, NULL, 0, 0, NULL, 0, '2025-06-07 20:08:38', NULL, NULL, NULL, '2025-06-07 20:08:38', NULL, NULL);
INSERT INTO `aea_task_evaluate_info` VALUES (1931322402882678785, 1931322380912914433, 1930100364691464193, NULL, 0, 0, NULL, 0, '2025-06-07 20:08:38', NULL, NULL, NULL, '2025-06-07 20:08:38', NULL, NULL);

SET FOREIGN_KEY_CHECKS = 1;
