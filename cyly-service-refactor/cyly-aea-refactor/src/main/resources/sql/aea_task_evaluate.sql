/*
 Navicat Premium Data Transfer

 Source Server         : *************
 Source Server Type    : MySQL
 Source Server Version : 80024 (8.0.24)
 Source Host           : *************:3306
 Source Schema         : cyly-aea

 Target Server Type    : MySQL
 Target Server Version : 80024 (8.0.24)
 File Encoding         : 65001

 Date: 08/06/2025 19:12:24
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for aea_task_evaluate
-- ----------------------------
DROP TABLE IF EXISTS `aea_task_evaluate`;
CREATE TABLE `aea_task_evaluate`  (
  `id` bigint NOT NULL COMMENT '主键',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门id',
  `task_num` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '任务编号',
  `assessor_evaluate_user_id` bigint NULL DEFAULT NULL COMMENT '主评估员用户id(app_user表)',
  `vice_evaluate_user_id` bigint NULL DEFAULT NULL COMMENT '副评估员用户id(app_user表)',
  `elder_id` bigint NULL DEFAULT NULL COMMENT '长者用户id(aea_elder表)',
  `scheme_code` bigint NULL DEFAULT NULL COMMENT '评估问卷表id(aea_questionnaire表的id)',
  `reason_code` int NULL DEFAULT NULL COMMENT '评估原因  1首次评估  2常规评估  3即时评估  4因对评估结果有疑问进行复评  5退出服务评估-包括服务最后三天   6退出服务跟进评估  7其他-例如上诉、研究',
  `expected_start_time` datetime NULL DEFAULT NULL COMMENT '期望开始时间',
  `expected_end_time` datetime NULL DEFAULT NULL COMMENT '期望完成时间',
  `plan_end_time` datetime NULL DEFAULT NULL COMMENT '评估员计划完成时间',
  `start_time` datetime NULL DEFAULT NULL COMMENT '任务实际开始时间',
  `end_time` datetime NULL DEFAULT NULL COMMENT '任务实际结束时间',
  `start_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务签到地址',
  `start_mark_url` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '任务签到多媒体信息存放的url地址',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '任务状态: \n        0-待执行, \n        1-执行中, \n        2-执行成功, \n        3-执行失败',
  `audit_status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态: \n        0-无审核, \n        1-待审核, \n        2-审核通过, \n        3-审核驳回',
  `source` tinyint NOT NULL COMMENT '数据来源: \\n        0-评估师, \\n        1-平台, \\n        2-民政',
  `del_flag` tinyint NOT NULL DEFAULT 0 COMMENT '删除状态: \n        0-否, \n        1-是',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NULL DEFAULT NULL COMMENT '备注信息',
  `assign_user_id` bigint NULL DEFAULT NULL COMMENT '进行分派操作的用户id',
  `assign_source` tinyint NULL DEFAULT NULL COMMENT '进行分派操作的用户来源 0：评估员 1：机构 2：民政',
  `assign_time` datetime NULL DEFAULT NULL COMMENT '分派时间',
  `assign_dept_time` datetime NULL DEFAULT NULL COMMENT '分派到机构时间',
  `create_dept` bigint NULL DEFAULT NULL COMMENT '创建机构',
  `create_by` bigint NULL DEFAULT NULL COMMENT '创建者id',
  `create_time` datetime NULL DEFAULT NULL COMMENT '创建时间',
  `update_by` bigint NULL DEFAULT NULL COMMENT '更新者id',
  `update_time` datetime NULL DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_general_ci COMMENT = '任务记录表' ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of aea_task_evaluate
-- ----------------------------
INSERT INTO `aea_task_evaluate` VALUES (1931322380912914433, NULL, 'AE2025060720083200000001', 1910267489927868418, 1910949462379900930, 1929791583771910146, 1927985268001239041, 1, NULL, NULL, NULL, NULL, NULL, NULL, NULL, 0, 0, 0, 0, NULL, NULL, NULL, '2025-06-07 20:08:32', NULL, NULL, NULL, '2025-06-07 20:08:33', NULL, '2025-06-07 20:08:33');

SET FOREIGN_KEY_CHECKS = 1;
