# PDF生成系统重构说明文档

## 📋 重构概述

本文档说明了基于Word模板结构的PDF生成系统重构，将原有的数据映射方式改进为更加结构化和可维护的方式。

**重构目标**: 基于Word模板`参考模版.docx`的四个主要部分，实现完整的PDF字段映射和数据填充。

**重构文件**: `AeaEvaluateReportServiceImpl.java`

## 🏗️ Word模板结构分析

### 模板四大部分

```
Section 1: 封面 + 表A.1 评估信息
├── 评估机构信息
├── 老人基本信息
├── 评估编号和时间
└── 评估人员信息

Section 2: 基本信息表组 (A.2-A.5)
├── 表A.2 评估对象基本信息表
├── 表A.3 信息提供者及联系人信息表  
├── 表A.4 疾病诊断和用药情况表
└── 表A.5 健康相关问题

Section 3: 能力评估表组 (B.1-B.4)
├── 表B.1 自理能力评估表 (8个评估项)
├── 表B.2 基础运动能力评估表 (4个评估项)
├── 表B.3 精神状态评估表 (9个评估项)
└── 表B.4 感知觉与社会参与评估表 (5个评估项)

Section 4: 总分统计和评估报告
├── 表B.5 老年人能力总分
├── 等级评定和调整
├── 评估结论
└── 签名信息
```

## 🔄 重构前后对比

### 重构前的问题
1. **数据映射混乱**: 五个数据块结构不清晰
2. **字段映射不完整**: 缺少对Word模板字段的完整映射
3. **代码可维护性差**: 硬编码的字段映射逻辑
4. **扩展性有限**: 难以适应模板变更

### 重构后的优势
1. **结构化数据映射**: 按模板四大部分组织数据
2. **完整字段覆盖**: 覆盖Word模板的所有表单域
3. **模块化设计**: 每个表格有独立的数据构建方法
4. **易于维护**: 清晰的方法分工和注释

## 📊 核心重构内容

### 1. buildReportData方法重构

#### 重构前结构
```java
// 原有的五个数据块结构
/*-------------------------第一节PDF数据----------------------------------------*/
/*-------------------------第二节PDF数据----------------------------------------*/
/*--------------------------第三节PDF数据---------------------------------*/
/*--------------------------第四节PDF数据---------------------------------*/
/*----------------------------------第五节------------------------------------*/
```

#### 重构后结构
```java
// 基于Word模板的四个部分结构
/*=========================== SECTION 1: 封面 + 表A.1 评估信息 ===========================*/
/*=========================== SECTION 2: 基本信息表组 ===========================*/
/*=========================== SECTION 3: 能力评估表组 ===========================*/
/*=========================== SECTION 4: 总分统计和评估报告 ===========================*/
```

### 2. 数据分类和映射方法

#### 新增分类方法
```java
// 基本信息记录分类
private Map<String, List<AeaTaskEvaluateExecutionRecordVo>> categorizeBasicInfoRecords()

// 评估记录分类  
private Map<String, List<AeaTaskEvaluateExecutionRecordVo>> categorizeAssessmentRecords()

// 分类判断逻辑
private String determineBasicInfoCategory()
private String determineAssessmentDimension()
```

#### 表格数据构建方法
```java
// Section 2: 基本信息表组
private void buildTableA2Data() // 评估对象基本信息表
private void buildTableA3Data() // 信息提供者信息表
private void buildTableA4Data() // 疾病诊断用药表
private void buildTableA5Data() // 健康相关问题表

// Section 3: 能力评估表组
private void buildTableB1Data() // 自理能力评估表 (B.1.1-B.1.8)
private void buildTableB2Data() // 基础运动能力评估表 (B.2.1-B.2.4)
private void buildTableB3Data() // 精神状态评估表 (B.3.1-B.3.9)
private void buildTableB4Data() // 感知觉与社会参与评估表 (B.4.1-B.4.5)

// Section 4: 总分和报告
private void buildTableB5ScoreData() // 总分统计
private void buildAssessmentReportData() // 评估报告
private void buildTimeAndSignatureData() // 时间和签名
```

### 3. 字段映射优化

#### 详细字段映射方法
```java
// 基本信息字段映射
private void mapBasicInfoFields() {
    // 姓名、性别、出生日期、身高、体重等13个基本字段
}

// 信息提供者字段映射
private void mapProviderInfoFields() {
    // 提供者姓名、关系、联系人信息等4个字段
}

// 疾病诊断字段映射
private void mapDiseaseInfoFields() {
    // 疾病诊断相关字段
}

// 健康问题字段映射  
private void mapHealthInfoFields() {
    // 17个健康相关风险评估字段
}
```

## 🎯 关键技术实现

### 1. 数据分类策略

#### 基本信息分类
```java
private String determineBasicInfoCategory(AeaTaskEvaluateExecutionRecordVo record) {
    Long questionId = record.getQuestionId();
    
    if (questionId >= 1 && questionId <= 20) {
        return "BASIC_INFO"; // 表A.2 评估对象基本信息
    } else if (questionId >= 21 && questionId <= 30) {
        return "PROVIDER_INFO"; // 表A.3 信息提供者信息
    } else if (questionId >= 31 && questionId <= 50) {
        return "DISEASE_INFO"; // 表A.4 疾病诊断用药
    } else if (questionId >= 51 && questionId <= 70) {
        return "HEALTH_INFO"; // 表A.5 健康相关问题
    }
    
    return "BASIC_INFO"; // 默认分类
}
```

#### 评估维度分类
```java
private String determineAssessmentDimension(AeaTaskEvaluateExecutionRecordVo record) {
    Long questionId = record.getQuestionId();
    
    if (questionId >= 100 && questionId <= 120) {
        return "SELF_CARE"; // 自理能力评估
    } else if (questionId >= 121 && questionId <= 140) {
        return "BASIC_MOBILITY"; // 基础运动能力评估
    } else if (questionId >= 141 && questionId <= 170) {
        return "MENTAL_STATE"; // 精神状态评估
    } else if (questionId >= 171 && questionId <= 190) {
        return "PERCEPTION_SOCIAL"; // 感知觉与社会参与评估
    }
    
    return "SELF_CARE"; // 默认分类
}
```

### 2. PDF字段映射

#### 与GBReportTemplate的对应关系
```java
// 自理能力评估 - 对应GBReportTemplate中的B.1.1-B.1.8字段
private void buildTableB1Data(Map<String, String> data, List<AeaTaskEvaluateExecutionRecordVo> records) {
    for (int i = 0; i < Math.min(records.size(), 8); i++) {
        AeaTaskEvaluateExecutionRecordVo record = records.get(i);
        String fieldKey = "B.1." + (i + 1);
        data.put(fieldKey, record.getScore() != null ? record.getScore().toString() : "0");
    }
}
```

#### 基本信息字段映射示例
```java
private void mapBasicInfoFields(Map<String, String> data, AeaTaskEvaluateExecutionRecordVo record) {
    Long questionId = record.getQuestionId();
    String answerContent = record.getContent();
    Long answerId = record.getAnswerId();
    
    switch (questionId.intValue()) {
        case 1: // 姓名
            data.put("name", answerContent != null ? answerContent : "");
            break;
        case 2: // 性别
            data.put("gender", answerId != null ? answerId.toString() : "");
            break;
        case 3: // 出生日期
            data.put("birthday", answerContent != null ? answerContent : "");
            break;
        // ... 更多字段映射
    }
}
```

### 3. 时间和签名处理

#### 时间格式化
```java
private void buildTimeAndSignatureData(Map<String, String> data, AeaEvaluateReportVo report, AeaTaskEvaluateVo task) {
    if (task.getStartTime() != null) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String[] dateParts = sdf.format(task.getStartTime()).split("-");
        data.put("year", dateParts[0]);
        data.put("month", dateParts[1]);
        data.put("day", dateParts[2]);
        data.put("year2", dateParts[0]);
        data.put("month2", dateParts[1]);
        data.put("day2", dateParts[2]);
    }
}
```

## 🔧 配置和扩展

### 1. 问题ID范围配置

当前实现使用硬编码的问题ID范围进行分类，实际使用时需要根据具体的问卷结构调整：

```java
// 需要根据实际问卷调整的ID范围
基本信息表: questionId 1-20
信息提供者: questionId 21-30  
疾病诊断: questionId 31-50
健康问题: questionId 51-70
自理能力: questionId 100-120
基础运动: questionId 121-140
精神状态: questionId 141-170
感知觉: questionId 171-190
```

### 2. 字段映射扩展

添加新的字段映射时，只需要在对应的映射方法中添加新的case分支：

```java
case 新问题ID: // 新字段描述
    data.put("新PDF字段名", 处理逻辑);
    break;
```

### 3. 新表格支持

如果Word模板增加新的表格，可以按照现有模式添加：

```java
// 1. 在categorizeBasicInfoRecords或categorizeAssessmentRecords中添加新分类
// 2. 创建新的buildTableXXData方法
// 3. 在buildReportData中调用新方法
```

## 📋 使用说明

### 1. 数据准备

确保传入的数据包含：
- `AeaEvaluateReportVo report`: 评估报告基本信息
- `AeaTaskEvaluateVo task`: 任务信息
- `List<AeaTaskEvaluateInfoVo> evaluateInfoVoList`: 评估详情列表
- `List<AeaTaskEvaluateExecutionRecordVo> records`: 执行记录列表

### 2. 调用方式

```java
// 构建PDF数据
Map<String, String> reportData = buildReportData(report, task, evaluateInfoVoList, records);

// 调用GBReportTemplate生成PDF
File pdfFile = gbReportTemplate.generateReportExec(
    reportData,
    orgName,
    mainUserAssessor,
    deputyUserAssessor,
    location,
    taskMedicineList,
    assessorSign,
    deputySign,
    familySign
);
```

### 3. 验证和调试

- 检查日志输出确认数据分类正确
- 验证PDF字段是否正确填充
- 确认中文字符显示正常
- 测试签名图片插入功能

## 🚀 后续优化建议

1. **配置化管理**: 将问题ID范围配置移到配置文件中
2. **动态字段映射**: 支持通过配置文件定义字段映射关系
3. **模板版本管理**: 支持多版本Word模板
4. **性能优化**: 对大量数据的处理进行性能优化
5. **错误处理**: 增强异常处理和数据验证

**重构完成时间**: 2024-12-19  
**重构人员**: cyly  
**版本**: v2.0
