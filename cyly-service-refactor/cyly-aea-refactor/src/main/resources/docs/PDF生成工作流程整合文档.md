# 老年人能力评估PDF生成工作流程整合文档

## 概述

本文档描述了老年人能力评估系统中PDF报告生成的完整工作流程，包括数据收集、处理、映射和PDF生成的端到端实现。

## 系统架构

### 核心服务类

1. **AeaQuestionnaireEvaluateServiceImpl** - 问卷评估服务实现
   - 负责数据收集和整合
   - 调用PDF生成服务
   - 管理评估流程

2. **AeaEvaluateReportServiceImpl** - 评估报告服务实现
   - 负责PDF生成逻辑
   - 数据映射和格式化
   - 与GBReportTemplate集成

3. **GBReportTemplate** - PDF模板生成器
   - 基于iText框架
   - 处理中文字体
   - 表单字段填充

## 工作流程

### 第一步：数据收集 (AeaQuestionnaireEvaluateServiceImpl.collectReportData)

```java
private Map<String, Object> collectReportData(Long taskId) {
    Map<String, Object> collectedData = new LinkedHashMap<>();
    
    // 1. 获取任务基本信息
    AeaTaskEvaluateVo taskEvaluate = aeaTaskEvaluateMapper.selectVoById(taskId);
    collectedData.put("taskEvaluate", taskEvaluate);
    
    // 2. 获取基本信息表数据
    Map<String, Object> basicInfoData = collectBasicInfoData(taskId);
    collectedData.put("basicInfoData", basicInfoData);
    
    // 3. 获取能力评估数据
    Map<String, Object> assessmentData = collectAssessmentData(taskId);
    collectedData.put("assessmentData", assessmentData);
    
    // 4. 计算评估结果
    Map<String, Object> evaluationResults = calculateEvaluationResults(collectedData);
    collectedData.putAll(evaluationResults);
    
    return collectedData;
}
```

### 第二步：创建报告记录 (AeaQuestionnaireEvaluateServiceImpl.createReportRecord)

```java
private AeaEvaluateReport createReportRecord(AeaEvaluateReportBo reportBo, Map<String, Object> collectedData) {
    // 1. 计算各维度分数
    Map<String, Integer> dimensionScores = calculateDimensionScores(collectedData);
    
    // 2. 创建报告记录
    AeaEvaluateReport report = BeanUtil.copyProperties(reportBo, AeaEvaluateReport.class);
    report.setCode(generateReportCode());
    
    // 3. 设置各维度分数
    report.setSelfScore(dimensionScores.get("selfCare"));
    report.setBaseScore(dimensionScores.get("basicMobility"));
    report.setMentionScore(dimensionScores.get("mentalState"));
    report.setFeelScore(dimensionScores.get("perceptionSocial"));
    report.setTotalScore(dimensionScores.get("total"));
    
    // 4. 保存到数据库
    aeaEvaluateReportMapper.insert(report);
    
    return report;
}
```

### 第三步：PDF生成 (AeaEvaluateReportServiceImpl.generatePdfReportIntegrated)

```java
public String generatePdfReportIntegrated(Long reportId, Map<String, Object> collectedData) {
    // 1. 获取报告记录
    AeaEvaluateReportVo report = baseMapper.selectVoById(reportId);
    
    // 2. 构建PDF数据结构
    Map<String, String> pdfData = buildReportDataFromCollected(report, collectedData);
    
    // 3. 调用GBReportTemplate生成PDF
    String pdfUrl = generatePdfFileIntegrated(report, pdfData);
    
    // 4. 更新报告URL
    updateReportUrl(reportId, pdfUrl);
    
    return pdfUrl;
}
```

## 数据映射结构

### PDF模板字段映射

PDF模板基于Word模板结构，包含四个主要部分：

#### Section 1: 封面 + 表A.1 评估信息
- `eva_org` - 评估机构
- `elder_name` - 老人姓名
- `address` - 评估地点
- `assessor_name` - 评估人员
- `evaluate_code` - 评估编号
- `evaluate_time` - 评估时间
- `reasonCode` - 评估原因
- `evaBaseTime` - 评估基准时间

#### Section 2: 基本信息表组 (A.2-A.5)
- `elder_id_number` - 身份证号
- `elder_gender` - 性别
- `elder_age` - 年龄
- `elder_birth_date` - 出生日期
- `elder_education` - 文化程度
- `elder_marital_status` - 婚姻状况
- `elder_occupation` - 职业
- `elder_address` - 现住址
- `elder_phone` - 联系电话
- `elder_emergency_contact` - 紧急联系人

#### Section 3: 能力评估表组 (B.1-B.4)
- `B.1.1` - `B.1.8` - 自理能力评估 (进食、洗澡、修饰、穿衣、大便控制、小便控制、如厕、床椅转移)
- `B.2.1` - `B.2.4` - 基础运动能力评估 (平地行走、上下楼梯、平衡、转移)
- `B.3.1` - `B.3.9` - 精神状态评估 (认知功能、攻击行为、抑郁症状等)
- `B.4.1` - `B.4.5` - 感知觉与社会参与评估 (视力、听力、沟通交流等)

#### Section 4: 总分统计和评估报告 (B.5 + 报告)
- `selfScore` - 自理能力得分
- `baseScore` - 基础运动能力得分
- `mentionScore` - 精神状态得分
- `feelScore` - 感知觉与社会参与得分
- `totalScore` - 总分
- `firstLevel` - 初评等级
- `finalLevel` - 终评等级
- `conclusion` - 评估结论
- `suggestion` - 建议

## 关键特性

### 1. 数据一致性
- 统一的数据收集接口
- 完整的数据验证机制
- 事务性操作保证数据完整性

### 2. 模板化PDF生成
- 基于Word模板的PDF结构
- 支持中文字体渲染
- 表单字段自动填充

### 3. 错误处理
- 完善的异常捕获和处理
- 详细的日志记录
- 用户友好的错误信息

### 4. 性能优化
- 批量数据查询
- 缓存机制
- 异步处理支持

## 使用示例

### 生成PDF报告

```java
// 1. 准备报告业务对象
AeaEvaluateReportBo reportBo = new AeaEvaluateReportBo();
reportBo.setTaskId(taskId);
reportBo.setLocation("评估地点");

// 2. 调用PDF生成服务
String pdfUrl = aeaQuestionnaireEvaluateService.generateReportPdf(reportBo);

// 3. 返回PDF下载链接
return pdfUrl;
```

### 重新生成PDF

```java
// 根据任务ID重新生成PDF
String pdfUrl = aeaQuestionnaireEvaluateService.regeneratePdfReport(taskId);
```

## 配置要求

### 1. 模板文件
- PDF模板文件位置: `src/main/resources/templates/老年人能力评估报告模板.pdf`
- 确保模板包含所有必要的表单字段

### 2. 字体支持
- 配置中文字体文件
- 确保iText框架正确加载字体

### 3. 文件存储
- 配置PDF文件存储路径
- 确保有足够的磁盘空间

## 扩展性

### 1. 新增字段
- 在PDF模板中添加新的表单字段
- 在数据映射方法中添加对应的字段映射
- 更新数据收集逻辑

### 2. 自定义模板
- 支持多种PDF模板
- 根据评估类型选择不同模板
- 动态模板配置

### 3. 国际化支持
- 多语言PDF模板
- 本地化数据格式
- 文化相关的评估标准

## 维护指南

### 1. 日志监控
- 关键操作的日志记录
- 性能指标监控
- 错误率统计

### 2. 数据备份
- 定期备份评估数据
- PDF文件归档策略
- 数据恢复机制

### 3. 版本管理
- PDF模板版本控制
- 数据结构变更管理
- 向后兼容性保证

## 总结

本整合方案实现了从问卷评估到PDF报告生成的完整工作流程，具有以下优势：

1. **完整性** - 覆盖了整个评估流程的所有环节
2. **可靠性** - 完善的错误处理和数据验证机制
3. **可维护性** - 清晰的代码结构和详细的文档
4. **可扩展性** - 支持未来功能扩展和定制需求
5. **性能** - 优化的数据处理和PDF生成流程

通过这个整合方案，系统能够高效、准确地生成符合国标要求的老年人能力评估PDF报告。
