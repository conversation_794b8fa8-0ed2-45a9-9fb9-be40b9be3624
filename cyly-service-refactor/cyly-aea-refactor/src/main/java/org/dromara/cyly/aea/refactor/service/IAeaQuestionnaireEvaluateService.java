package org.dromara.cyly.aea.refactor.service;

import org.dromara.cyly.aea.refactor.domain.bo.AeaEvaluateReportBo;
import org.dromara.cyly.aea.refactor.domain.bo.AeaTaskEvaluateBo;
import org.dromara.cyly.aea.refactor.domain.bo.AeaTaskSubmitAnswerBo;
import org.dromara.cyly.aea.refactor.domain.bo.BatchAnswerSubmitBo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireQuestionVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaTaskEvaluateInfoVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * 问卷评估流程服务
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
public interface IAeaQuestionnaireEvaluateService {

    Boolean startEvaluate(Long taskId);

    @Transactional(rollbackFor = Exception.class)
    Long createEvaluateTaskFlow(AeaTaskEvaluateBo aeaTaskEvaluate);

    List<AeaTaskEvaluateInfoVo> getAeaTaskEvaluateInfoList(Long taskId);

    Boolean submitAnswers(AeaTaskSubmitAnswerBo bo);


    Map<String, Object> getEvaluateReport(Long taskId, Long questionnaireId);


    /**
     * 获取问卷结构信息
     * @param questionnaireId 问卷ID
     * @return 问卷结构
     */
    AeaQuestionnaireVo getQuestionnaireStructure(Long questionnaireId);

    /**
     * 获取问卷问题和选项
     * @param questionnaireId 问卷ID
     * @return 问题和选项列表
     */
    List<AeaQuestionnaireQuestionVo> getQuestionnaireQuestions(Long questionnaireId);


    /**
     * 获取评估报告下载链接
     * @param taskId 任务ID
     * @return 下载链接
     */
    String getReportDownloadUrl(Long taskId);

    /**
     * 重新生成PDF报告
     * @param taskId 任务ID
     * @return 新的下载链接
     */
    String regeneratePdfReport(Long taskId);

    /**
     * 第五步：生成PDF报告
     * 完整实现PDF报告生成流程
     * @param aeaEvaluateReportBo 评估报告业务对象
     * @return PDF报告下载链接
     */
    String generateReportPdf(AeaEvaluateReportBo aeaEvaluateReportBo);
}