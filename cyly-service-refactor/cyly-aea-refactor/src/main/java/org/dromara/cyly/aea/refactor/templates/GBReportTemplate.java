package org.dromara.cyly.aea.refactor.templates;

import com.itextpdf.forms.PdfAcroForm;
import com.itextpdf.forms.fields.PdfFormField;
import com.itextpdf.io.font.PdfEncodings;
import com.itextpdf.io.image.ImageDataFactory;
import com.itextpdf.kernel.colors.DeviceRgb;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfReader;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.kernel.pdf.annot.PdfWidgetAnnotation;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Image;
import org.dromara.cyly.aea.refactor.config.ReportPathProperties;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.core.io.ResourceLoader;
import org.springframework.stereotype.Component;

import java.io.File;
import java.io.IOException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;


/**
 * 国标评估报告模板
 *
 * <AUTHOR>
 */
@Component
public class GBReportTemplate {

    /**
     * 报告路径配置属性
     */
    @Autowired
    private ReportPathProperties reportPathProperties;

    @Autowired
    private ResourceLoader resourceLoader;

    /**
     * 生成评估报告
     *
     * @param map1 评估数据
     * @param orgName 机构名称
     * @param mainUserAssessor 主评估人
     * @param deputyUserAssessor 副评估人
     * @param location 评估地点
     * @param taskMedicineList 任务药物列表
     * @param assessorSign 评估人签名
     * @param deputySign 副评估人签名
     * @param familySign 家属签名
     * @return 生成的报告文件
     * @throws Exception 如果生成过程中发生错误
     */
    public File generateReportExec(Map<String,String> map1, String orgName, String mainUserAssessor,
                                  String deputyUserAssessor, String location,
                                  List<Object> taskMedicineList, String assessorSign, String deputySign,
                                  String familySign) throws Exception {

        log.info("开始生成PDF报告，数据项数量: {}", map1.size());

        // 使用模板路径
        String templatePath = reportPathProperties.getTemplatePath();
        if (templatePath == null || templatePath.trim().isEmpty()) {
            // 默认使用classpath下的模板
            templatePath = "classpath:templates/参考模版.pdf";
        }

        Resource templateResource = resourceLoader.getResource(templatePath);
        if (!templateResource.exists()) {
            throw new FileNotFoundException("PDF模板文件不存在: " + templatePath);
        }

        // 输出目录和文件名
        String dirPath = reportPathProperties.getOutputDir();
        String elderName = map1.getOrDefault("name", "未知");
        String evalDate = map1.getOrDefault("evaBaseTime", "");
        String fileName = elderName + "_" + evalDate + "_老年人能力评估报告.pdf";
        String outputPath = dirPath + File.separator + fileName;

        // 确保输出目录存在
        File outputDir = new File(dirPath);
        if (!outputDir.exists()) {
            boolean created = outputDir.mkdirs();
            log.info("创建输出目录: {}, 结果: {}", dirPath, created);
        }

        // 创建输出文件
        File outputFile = new File(outputPath);
        log.info("PDF输出路径: {}", outputPath);

        // 字体路径
        Path fontPath = Paths.get(reportPathProperties.getFontPath());
        if (!Files.exists(fontPath)) {
            throw new FileNotFoundException("字体文件不存在: " + fontPath);
        }

        // 创建PDF文档
        PdfDocument pdfDocument = new PdfDocument(
            new PdfReader(templateResource.getInputStream()),
            new PdfWriter(outputFile)
        );
        // 表单域
        PdfAcroForm form = PdfAcroForm.getAcroForm(pdfDocument, false);


        // 字体 // "UniGB-UTF16-H"
        PdfFont font = PdfFontFactory.createFont(
                fontPath.toAbsolutePath().toString(), PdfEncodings.IDENTITY_H, true);
        // 字体颜色：黑色
        DeviceRgb color = new DeviceRgb(0, 0, 0);
        // 文本填充
        Map<String, String> map = new HashMap<>();
        //复选框填充
        Map<String, String> checkMap = new HashMap<>();
        //单选框填充
        Map<String,String> singleMap = new HashMap<>();

        //图片地址
        addSignatureImage(form,"image1",assessorSign,pdfDocument);
        addSignatureImage(form,"image2",deputySign,pdfDocument);
        addSignatureImage(form,"image3",familySign,pdfDocument);

        //封面新增 //评估机构
        map.put("eva_org", orgName);
        //评估人员
        map.put("assessor_name", mainUserAssessor + "," + deputyUserAssessor);
        //老人姓名
        map.put("elder_name", map1.get("name"));
        //评估地点
        map.put("address", location);

        map.put("evaluate_code",map1.getOrDefault("evaCode",""));
        map.put("evaluate_time",map1.getOrDefault("evaBaseTime",""));

        String reasonCode = map1.getOrDefault("reasonCode", "");
        //评估原因
        switch (reasonCode){
            case "4":
                checkMap.put("Check Box1","√");
                break;
            case "5":
                checkMap.put("Check Box2","√");
                break;
            case "6":
                checkMap.put("Check Box3","√");
                break;
            case "7":
                checkMap.put("Check Box4","√");
                break;
            case "8":
                checkMap.put("Check Box5","√");
                break;
        }

        map.put("evaluate_name",map1.getOrDefault("name",""));

        //性别
        String gender = map1.getOrDefault("gender", "");
        switch (gender){
            case "11" :
                checkMap.put("Check Box6","√");
                break;
            case "12" :
                checkMap.put("Check Box7","√");
                break;
        }

        map.put("birthday", map1.getOrDefault("birthday",""));
        map.put("height", map1.getOrDefault("height",""));
        map.put("weight", map1.getOrDefault("weight",""));

        String ethnic = map1.getOrDefault("ethnic","");
        String[] ethnicArr = ethnic.split(",");
        String chinese = chinese(ethnicArr);
        for (String s : ethnicArr){
           switch (s){
               case "17" :
                   checkMap.put("Check Box8", "√");
                   break;
               case "18" :
                   checkMap.put("Check Box9", "√");

                   map.put("otherEthnic",chinese);
           }
       }

        String[] religion = map1.getOrDefault("religion","").split(",");
        String religionStr = chinese(religion);
        for (String s : religion){
           switch (s){
               case "20" :
                   checkMap.put("Check Box10", "√");
                   break;
               case "21" :
                   checkMap.put("Check Box11", "√");
                   map.put("religion",religionStr);
                   break;
           }
       }



        map.put("id_number", map1.getOrDefault("idnumber",""));

            switch (map1.getOrDefault("education","")) {
                case "24":
                    checkMap.put("Check Box12", "√");
                    break;
                case "25":
                    checkMap.put("Check Box13", "√");
                    break;
                case "26":
                    checkMap.put("Check Box14", "√");
                    break;
                case "27":
                    checkMap.put("Check Box15", "√");
                    break;
                case "28":
                    checkMap.put("Check Box16", "√");
                    break;
                case "29" :
                    checkMap.put("Check Box17", "√");
                    break;
            }


        String residence = map1.getOrDefault("residence","");
        String[] residenceSplit = residence.split(",");
        for (String s : residenceSplit){
            switch (s) {
                case "31":
                    checkMap.put("Check Box18", "√");
                    break;
                case "32":
                    checkMap.put("Check Box19", "√");
                    break;
                case "33":
                    checkMap.put("Check Box20", "√");
                    break;
                case "34":
                    checkMap.put("Check Box21", "√");
                    break;
                case "35":
                    checkMap.put("Check Box22", "√");
                    break;
                case "36":
                    checkMap.put("Check Box23", "√");
                    break;
                case "37":
                    checkMap.put("Check Box24", "√");
                    break;
                case "38":
                    checkMap.put("Check Box25", "√");
                    break;

            }
        }
//

        switch (map1.getOrDefault("marriage","")){
            case "40" :
                checkMap.put("Check Box26", "√");
                break;
            case "41" :
                checkMap.put("Check Box27", "√");
                break;
            case "42" :
                checkMap.put("Check Box28", "√");
                break;
            case "43" :
                checkMap.put("Check Box29", "√");
                break;
            case "44" :
                checkMap.put("Check Box30", "√");
                break;
        }
        String paymentCodes = map1.getOrDefault("medical","");
        String[] paymentCodeArr = paymentCodes.split(",");
        for (String paymentCode : paymentCodeArr) {
            switch (paymentCode) {
                case "46":
                    checkMap.put("Check Box31", "√");
                    break;
                case "47":
                    checkMap.put("Check Box32", "√");
                    break;
                case "48":
                    checkMap.put("Check Box33", "√");
                    break;
                case "49":
                    checkMap.put("Check Box34", "√");
                    break;
                case "50":
                    checkMap.put("Check Box35", "√");
                    break;
                case "51":
                    checkMap.put("Check Box36", "√");
                    break;
                case "52":
                    checkMap.put("Check Box37", "√");
                    break;
                case "53":
                    checkMap.put("Check Box38", "√");
                    break;
            }
        }
        String incomeCodes = map1.getOrDefault("income","");
        String[] incomeCodeArr = incomeCodes.split(",");
        for (String incomeCode : incomeCodeArr) {
            switch (incomeCode) {
                case "55":
                    checkMap.put("Check Box39", "√");
                    break;
                case "56":
                    checkMap.put("Check Box40", "√");
                    break;
                case "57":
                    checkMap.put("Check Box41", "√");
                    break;
                case "58":
                    checkMap.put("Check Box42", "√");
                    break;
                case "59":
                    checkMap.put("Check Box43", "√");
                    break;
                case "60":
                    checkMap.put("Check Box64", "√");
                    break;
            }
        }

        String fail = map1.getOrDefault("fall","");
        switch (fail) {
            case "63":
                checkMap.put("Check Box44", "√");
                break;
            case "64":
                checkMap.put("Check Box45", "√");
                break;
            case "65":
                checkMap.put("Check Box46", "√");
                break;
            case "66":
                checkMap.put("Check Box47", "√");
                break;
        }
        String lost = map1.getOrDefault("lost","");
        switch (lost) {
            case "68":
                checkMap.put("Check Box48", "√");
                break;
            case "69":
                checkMap.put("Check Box49", "√");
                break;
            case "70":
                checkMap.put("Check Box50", "√");
                break;
            case "71":
                checkMap.put("Check Box51", "√");
                break;
        }
        String choking = map1.getOrDefault("choking","");
        switch (choking) {
            case "73":
                checkMap.put("Check Box52", "√");
                break;
            case "74":
                checkMap.put("Check Box53", "√");
                break;
            case "75":
                checkMap.put("Check Box54", "√");
                break;
            case "76":
                checkMap.put("Check Box55", "√");
                break;
        }
        String suicide = map1.getOrDefault("suicide","");
        switch (suicide) {
            case "78":
                checkMap.put("Check Box56", "√");
                break;
            case "79":
                checkMap.put("Check Box57", "√");
                break;
            case "80":
                checkMap.put("Check Box58", "√");
                break;
            case "81":
                checkMap.put("Check Box59", "√");
                break;
        }

        String other = map1.getOrDefault("other","");
        switch (other) {
            case "83":
                checkMap.put("Check Box60", "√");
                break;
            case "84":
                checkMap.put("Check Box61", "√");
                break;
            case "85":
                checkMap.put("Check Box62", "√");
                break;
            case "86":
                checkMap.put("Check Box63", "√");
                break;
        }
        map.put("relationship_name", map1.getOrDefault("provideName",""));


        String[] provideRelation = map1.getOrDefault("provideRelation","").split(",");
        String provide = chinese(provideRelation);
        for (String relationship : provideRelation){
            switch (relationship) {
                case "89":
                    checkMap.put("Check Box65", "√");
                    break;
                case "90":
                    checkMap.put("Check Box66", "√");
                    break;
                case "91":
                    checkMap.put("Check Box67", "√");
                    break;
                case "92":
                    checkMap.put("Check Box68", "√");
                    break;
                case "93":
                    checkMap.put("Check Box69", "√");
                    break;
                case "94":
                    checkMap.put("Check Box70", "√");
                    break;
                case "95" :
                    checkMap.put("Check Box71", "√");
                    map.put("otherRelation",provide);
                    break;
            }
        }


        map.put("ContactName", map1.getOrDefault("relationName",""));
        map.put("ContactMobile",map1.getOrDefault("relationMobile",""));




        String[] codeArr = map1.getOrDefault("illness","").split(",");
        String illness = chinese(codeArr);
        for (String code : codeArr) {
               switch (code){
                   case "99":
                       checkMap.put("Check Box145","√");
                       break;
                   case "100":
                       checkMap.put("Check Box146","√");
                       break;
                   case "101":
                       checkMap.put("Check Box147","√");
                       break;
                   case "102":
                       checkMap.put("Check Box148","√");
                       break;
                   case "103":
                       checkMap.put("Check Box149","√");
                       break;
                   case "104":
                       checkMap.put("Check Box150","√");
                       break;
                   case "105":
                       checkMap.put("Check Box151","√");
                       break;
                   case "106":
                       checkMap.put("Check Box152","√");
                       break;
                   case "107":
                       checkMap.put("Check Box153","√");
                       break;
                   case "108":
                       checkMap.put("Check Box154","√");
                       break;
                   case "109":
                       checkMap.put("Check Box155","√");
                       break;
                   case "110":
                       checkMap.put("Check Box156","√");
                       break;
                   case "111":
                       checkMap.put("Check Box157","√");
                       break;
                   case "112":
                       checkMap.put("Check Box158","√");
                       break;
                   case "113":
                       checkMap.put("Check Box159","√");
                       break;
                   case "114":
                       checkMap.put("Check Box160","√");
                       break;
                   case "115":
                       checkMap.put("Check Box161","√");
                       break;
                   case "116":
                       checkMap.put("Check Box162","√");
                       break;
                   case "117":
                       checkMap.put("Check Box163","√");
                       break;
                   case "118":
                       checkMap.put("Check Box164","√");
                       break;
                   case "119":
                       checkMap.put("Check Box165","√");
                       break;
                   case "120":
                       checkMap.put("Check Box166","√");
                       break;
                   case "121":
                       checkMap.put("Check Box167","√");
                       map.put("other_illness",illness);
                       break;

               }

            }


        //用药情况
        int endIndex= Math.min(taskMedicineList.size(),4);
        List<Object> taskMedicines = new ArrayList<>();
        //暂时注销,导入逻辑重写
        /*List<AeaTaskMedicine> taskMedicines = taskMedicineList.subList(0, endIndex);
        for (int i=0;i<taskMedicines.size();i++){
            AeaTaskMedicine taskMedicine = taskMedicineList.get(i);
            map.put("drugName"+i,taskMedicine.getMedicineName() !=null ? taskMedicine.getMedicineName() : "");
            map.put("medicineWay"+i,taskMedicine.getMedicineMethod() !=null ? taskMedicine.getMedicineMethod() : "");
            map.put("dosing"+i,taskMedicine.getMedicineDosage() !=null ? taskMedicine.getMedicineDosage() : "");
            map.put("frequency"+i,taskMedicine.getMedicineFrequency() != null ? taskMedicine.getMedicineFrequency() : "");
        }*/
//
        switch (map1.getOrDefault("pressure","")) {
            case "126":
                checkMap.put("Check Box72", "√");
                break;
            case "127":
                checkMap.put("Check Box73", "√");
                break;
            case "128":
                checkMap.put("Check Box74", "√");
                break;
            case "129":
                checkMap.put("Check Box75", "√");
                break;
            case "130":
                checkMap.put("Check Box76", "√");
                break;
            case "131":
                checkMap.put("Check Box77", "√");
                break;
        }

        String[] joints = map1.getOrDefault("joint","").split(",");
        for (String joint : joints){
                switch (joint) {
                    case "133":
                        checkMap.put("Check Box78", "√");
                        break;
                    case "134":
                        checkMap.put("Check Box79", "√");
                        map.put("other_joint",chinese(joints));
                        break;
                    case "135":
                        checkMap.put("Check Box80", "√");

                        break;
                }
            }



        String[] wounds = map1.getOrDefault("wound","").split(",");
        for (String wound : wounds) {
            switch (wound) {
                case "137":
                    checkMap.put("Check Box81", "√");
                    break;
                case "138":
                    checkMap.put("Check Box82", "√");
                    break;
                case "139":
                    checkMap.put("Check Box83", "√");
                    break;
                case "140":
                    checkMap.put("Check Box84", "√");
                    break;
                case "141":
                    checkMap.put("Check Box85", "√");
                    break;
                case "142":
                    checkMap.put("Check Box86", "√");
                    break;
                case "143":
                    checkMap.put("Check Box87", "√");
                    break;
            }
        }


        String[] specialCare = map1.getOrDefault("specialCare","").split(",");
        for (String care : specialCare) {
            switch (care) {
                case "145":
                    checkMap.put("Check Box88", "√");
                    break;
                case "146":
                    checkMap.put("Check Box89", "√");
                    break;
                case "147":
                    checkMap.put("Check Box90", "√");
                    break;
                case "148":
                    checkMap.put("Check Box91", "√");
                    break;
                case "149":
                    checkMap.put("Check Box92", "√");
                    break;
                case "150":
                    checkMap.put("Check Box93", "√");
                    break;
                case "151":
                    checkMap.put("Check Box94", "√");
                    break;
                case "152":
                    checkMap.put("Check Box95", "√");
                    break;
            }
        }
        switch (map1.getOrDefault("pain","")) {
            case "154":
                checkMap.put("Check Box96", "√");
                break;
            case "155":
                checkMap.put("Check Box97", "√");
                break;
            case "156":
                checkMap.put("Check Box98", "√");
                break;
            case "157":
                checkMap.put("Check Box99", "√");
                break;
            case "158":
                checkMap.put("Check Box100", "√");
                break;
        }


        String[] toothArr = map1.getOrDefault("tooth","").split(",");
        for (String tooth : toothArr) {
            switch (tooth) {
                case "160":
                    checkMap.put("Check Box101", "√");
                    break;
                case "161":
                    checkMap.put("Check Box102", "√");
                    break;
                case "162":
                    checkMap.put("Check Box103", "√");
                    break;
                case "163" :
                    singleMap.put("signle1","√");
                    break;
                case "164" :
                    singleMap.put("signle2","√");
                    break;
                case "165" :
                    singleMap.put("signle3","√");
                    break;
                case "166":
                    checkMap.put("Check Box104", "√");
                    break;
                case "167" :
                    singleMap.put("signle4","√");
                    break;
                case "168" :
                    singleMap.put("signle5","√");
                    break;
                case "169" :
                    singleMap.put("signle6","√");
                    break;
            }
        }
        String[] falseTooth = map1.getOrDefault("falseTooth","").split(",");
        for (String tooth : falseTooth) {
            switch (tooth) {
                case "171":
                    checkMap.put("Check Box105", "√");
                    break;
                case "172":
                    checkMap.put("Check Box106", "√");
                    break;
                case "173":
                    checkMap.put("Check Box107", "√");
                    break;
                case "174":
                    checkMap.put("Check Box108", "√");
                    break;
            }
        }
        String[] swans = map1.getOrDefault("swan","").split(",");
        for (String swan : swans) {
            switch (swan) {
                case "176":
                    checkMap.put("Check Box109", "√");
                    break;
                case "177":
                    checkMap.put("Check Box110", "√");
                    break;
                case "178":
                    checkMap.put("Check Box111", "√");
                    break;
                case "179":
                    checkMap.put("Check Box112", "√");
                    break;
                case "180":
                    checkMap.put("Check Box113", "√");
                    break;
                case "181":
                    checkMap.put("Check Box114", "√");
                    break;
            }
        }
        switch (map1.getOrDefault("BMI","")) {
            case "183":
                checkMap.put("Check Box115", "√");
                break;
            case "184":
                checkMap.put("Check Box116", "√");
                break;
        }
        switch (map1.getOrDefault("clean","")) {
            case "186":
                checkMap.put("Check Box117", "√");
                break;
            case "187":
                checkMap.put("Check Box118", "√");
                break;
        }
        switch (map1.getOrDefault("disease","")) {
            case "189":
                checkMap.put("Check Box119", "√");
                break;
            case "190":
                checkMap.put("Check Box120", "√");
                break;
        }
            map.put("Text106",map1.getOrDefault("situation","") );
//
        map.put("Text107", map1.getOrDefault("B.1.1",""));
        map.put("Text108", map1.getOrDefault("B.1.2",""));
        map.put("Text109", map1.getOrDefault("B.1.3",""));
        map.put("Text110", map1.getOrDefault("B.1.4",""));
        map.put("Text111", map1.getOrDefault("B.1.5",""));
        map.put("Text112", map1.getOrDefault("B.1.6",""));
        map.put("Text113", map1.getOrDefault("B.1.7",""));
        map.put("Text114", map1.getOrDefault("B.1.8",""));

        map.put("Text135", map1.getOrDefault("selfScore",""));

        map.put("Text115", map1.getOrDefault("B.2.1",""));
        map.put("Text116", map1.getOrDefault("B.2.2",""));
        map.put("Text117", map1.getOrDefault("B.2.3",""));
        map.put("Text118", map1.getOrDefault("B.2.4",""));
        map.put("Text136", map1.getOrDefault("baseScore",""));

        map.put("Text119", map1.getOrDefault("B.3.1",""));
        map.put("Text120", map1.getOrDefault("B.3.2",""));
        map.put("Text121", map1.getOrDefault("B.3.3",""));
        map.put("Text122", map1.getOrDefault("B.3.4",""));
        map.put("Text123", map1.getOrDefault("B.3.5",""));
        map.put("Text124", map1.getOrDefault("B.3.6",""));
        map.put("Text125", map1.getOrDefault("B.3.7",""));
        map.put("Text126", map1.getOrDefault("B.3.8",""));
        map.put("Text127", map1.getOrDefault("B.3.9",""));

        map.put("Text128", map1.getOrDefault("mentionScore",""));

        map.put("Text129", map1.getOrDefault("B.4.1",""));
        map.put("Text130", map1.getOrDefault("B.4.2",""));
        map.put("Text131", map1.getOrDefault("B.4.3",""));
        map.put("Text132", map1.getOrDefault("B.4.4",""));
        map.put("Text133", map1.getOrDefault("B.4.5",""));

        map.put("Text134", map1.getOrDefault("feelScore",""));
        //总分
        map.put("Text137", map1.getOrDefault("totalScore",""));
//         //表C
        map.put("Text138", map1.getOrDefault("selfScore"," "));
        map.put("Text139", map1.getOrDefault("baseScore",""));
        map.put("Text140", map1.getOrDefault("mentionScore",""));
        map.put("Text141", map1.getOrDefault("feelScore",""));
        map.put("Text142", map1.getOrDefault("totalScore",""));
//

        switch (map1.getOrDefault("firstLevel","")) {
            case "1":
                checkMap.put("Check Box121", "√");
                break;
            case "2":
                checkMap.put("Check Box122", "√");
                break;
            case "3":
                checkMap.put("Check Box123", "√");
                break;
            case "4":
                checkMap.put("Check Box124", "√");
                break;
            case "5":
                checkMap.put("Check Box125", "√");
                break;
        }

        String change = map1.getOrDefault("change","");
        String[] changeSplit = change.split(",");
        for (String s : changeSplit){
            switch (s) {
                case "1":
                    checkMap.put("Check Box126", "√");
                    break;
                case "2":
                    checkMap.put("Check Box127", "√");
                    break;
                case "3":
                    checkMap.put("Check Box128", "√");
                    break;
            }
        }



//

        switch (map1.getOrDefault("finalLevel","")) {
            case "1":
                checkMap.put("Check Box129", "√");
                break;
            case "2":
                checkMap.put("Check Box130", "√");
                break;
            case "3":
                checkMap.put("Check Box131", "√");
                break;
            case "4":
                checkMap.put("Check Box132", "√");
                break;
            case "5":
                checkMap.put("Check Box133", "√");
                break;
        }
        map.put("evaluate_adress",location);
        String[] split = map1.getOrDefault("evaBaseTime","").split("-");
        map.put("year", split[0]);
        map.put("month",split[1]);
        map.put("day",split[2]);
        map.put("year2", split[0]);
        map.put("month2",split[1]);
        map.put("day2",split[2]);

//


        for (Map.Entry<String, String> entry : map.entrySet()) {
            System.out.println("\n\nentry.getKey()=" + entry.getKey());
            System.out.println("entry.getValue()=" + entry.getValue() + "\n");
            Map<String, PdfFormField> formFieldMap = form.getFormFields();

            if (formFieldMap == null) {
                throw new RuntimeException("域名为空");
            }
            PdfFormField pdfFormField = formFieldMap.get(entry.getKey());
            if (pdfFormField == null) {
                throw new RuntimeException(entry.getKey()+"域名为空");
            }
            pdfFormField.setValue(entry.getValue()).setColor(color).setFont(font).setFontSize(10f);
        }

        for (Map.Entry<String, String> entry : checkMap.entrySet()) {
            System.out.println("\n\nentry.getKey()=" + entry.getKey());
            System.out.println("entry.getValue()=" + entry.getValue() + "\n");
            Map<String, PdfFormField> formFieldMap = form.getFormFields();
            if (formFieldMap == null) {
                throw new RuntimeException("域名为空");
            }
            PdfFormField pdfFormField = formFieldMap.get(entry.getKey());
            if (pdfFormField == null) {
                throw new RuntimeException("域名为空");
            }
            pdfFormField.setCheckType(PdfFormField.TYPE_CHECK).setValue("on",true);
        }


        for (Map.Entry<String, String> entry : singleMap.entrySet()) {
            System.out.println("\n\nentry.getKey()=" + entry.getKey());
            System.out.println("entry.getValue()=" + entry.getValue() + "\n");
            Map<String, PdfFormField> formFieldMap = form.getFormFields();
            if (formFieldMap == null) {
                throw new RuntimeException("域名为空");
            }
            PdfFormField pdfFormField = formFieldMap.get(entry.getKey());
            if (pdfFormField == null) {
                throw new RuntimeException("域名为空");
            }
            pdfFormField.setCheckType(PdfFormField.TYPE_CIRCLE).setValue("on", true);
        }

        // 清除表单域
        form.flattenFields();
        pdfDocument.close();
//        Map<String,String> result = new HashMap<>();
//
//        //上传到oss
//        String objectName = map1.getOrDefault("name","");
//        String ossURL = AliyunOssUtil.uploadFileToOss(file1,objectName);
//        result.put("fileName",fileName);
//        result.put("ossURL",ossURL);

        return file1; //+ fileName
    }

    //抽取汉字
    public static String chinese(String[] str){
        StringBuffer chineseChars = new StringBuffer();
        Pattern pattern = Pattern.compile("[\\u4e00-\\u9fa5]+");

        for (String s :str){
            Matcher matcher = pattern.matcher(s);
            while (matcher.find()){
                chineseChars.append(matcher.group()).append(",");
            }
        }
        if (chineseChars.length()>0){
            chineseChars.deleteCharAt(chineseChars.length()-1);
        }


        return chineseChars.toString();
    }

    //图片签名
    private static void addSignatureImage(PdfAcroForm form,String fieldName,String imagePath,PdfDocument pdfDocument)throws IOException{
        Document document = new Document(pdfDocument);
        if (imagePath == null || imagePath.isEmpty()) {
//            throw new IllegalArgumentException("无效的图片路径");
            Map<String, String> imageMap = new HashMap<>();
            imageMap.put("fieldName", "");
        } else {

        PdfFormField field  = form.getField(fieldName);
        if(field == null){
            throw new IllegalArgumentException(fieldName+"不存在");
        }
        List<PdfWidgetAnnotation> widgets = field.getWidgets();
        if (!widgets.isEmpty()){
            PdfWidgetAnnotation widget = widgets.get(0);
            float x1 = widget.getRectangle().getAsNumber(0).floatValue();
            float y1 = widget.getRectangle().getAsNumber(1).floatValue();
            //域右上角顶点坐标
            float x2 = widget.getRectangle().getAsNumber(2).floatValue();
            float y2 = widget.getRectangle().getAsNumber(3).floatValue();

                //保证域宽高不为负
                float width = Math.max(x2 - x1, 0);
                float height = Math.max(y2 - y1, 0);

           try {

               Image image = new Image(ImageDataFactory.create(imagePath)).scaleToFit(width,height);
               float centerX = x1 + (width /  2) - (image.getImageScaledWidth() / 2);
               float centerY = y1 + (height / 2) - (image.getImageScaledHeight() /2);
               image.setFixedPosition(9,centerX,centerY);
               document.add(image);
           }catch (IOException e){
               throw new IOException("添加图片签名地址失败"+fieldName,e);
           }

            }
        }
    }
}
