package org.dromara.cyly.aea.refactor.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.enums.AbilityLevelEnum;
import org.dromara.common.core.enums.FormatsType;
import org.dromara.common.core.enums.SourceEnum;
import org.dromara.common.core.enums.TaskStatusEnum;
import org.dromara.common.core.utils.DateUtils;
import org.dromara.cyly.aea.refactor.domain.*;
import org.dromara.cyly.aea.refactor.domain.bo.*;
import org.dromara.cyly.aea.refactor.domain.vo.*;
import org.dromara.cyly.aea.refactor.mapper.*;
import org.dromara.cyly.aea.refactor.service.*;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 问卷评估核心业务服务实现
 *
 * <AUTHOR>
 * @date 2025-05-28
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class AeaQuestionnaireEvaluateServiceImpl implements IAeaQuestionnaireEvaluateService {

    private final IAeaTaskEvaluateService taskService;
    private final IAeaTaskEvaluateInfoService evaluateInfoService;
    private final IAeaTaskEvaluateExecutionRecordService executionRecordService;
    private final IAeaEvaluateReportService evaluateReportService;
    private final IAeaQuestionnaireService questionnaireService;
    private final IAeaQuestionnaireQuestionService questionnaireQuestionService;
    private final AeaQuestionnaireAnswerMapper aeaQuestionnaireAnswerMapper;
    private final AeaTaskEvaluateExecutionRecordMapper aeaTaskEvaluateExecutionRecordMapper;
    private final AeaTaskEvaluateInfoMapper aeaTaskEvaluateInfoMapper;
    private final AeaEvaluateReportMapper aeaEvaluateReportMapper;
    private final AeaElderHealthInfoMapper aeaElderHealthInfoMapper;
    private final AeaQuestionnaireMapper aeaQuestionnaireMapper;
    private final AeaTaskEvaluateMapper aeaTaskEvaluateMapper;


    /**
     * 开始评估，先查询是否有长者的建档信息
     */
    @Override
    public Boolean startEvaluate(Long taskId) {
        //  1. 查询长者的建档信息
        AeaTaskEvaluateVo task = taskService.queryById(taskId);
        // 2. 如果没有长者的建档信息，返回false
        Long rows = aeaElderHealthInfoMapper.selectCount(new QueryWrapper<AeaElderHealthInfo>().eq("aea_elder_id", task.getElderId()));
        if (rows == 0) {
            log.error("未查找到:{}的建档信息", task.getElderId());
            return false;
        }
        // 3. 如果有长者的建档信息，更新任务状态为进行中
        AeaTaskEvaluateBo taskBo = new AeaTaskEvaluateBo();
        taskBo.setId(task.getId());
        taskBo.setStartTime(new Date());
        taskBo.setStatus(TaskStatusEnum.IN_PROGRESS.getCode());
        taskBo.setUpdateTime(new Date());
        //  4. 更新任务
        return taskService.updateByBo(taskBo);
    }

    /**
     * 第一步：创建评估任务流程
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public Long createEvaluateTaskFlow(AeaTaskEvaluateBo aeaTaskEvaluate) {
        try {
            // 1. 创建评估任务
            aeaTaskEvaluate.setSource(SourceEnum.ASSESSOR.getCode());
            aeaTaskEvaluate.setAssignTime(DateUtils.getNowDate());
            aeaTaskEvaluate.setTaskNum(generateReportCode()); // 任务编号
            Long taskId = taskService.createEvaluateTask(aeaTaskEvaluate);
            log.info("创建评估任务成功，任务ID: {}", taskId);

            // 2. 查询评估问卷表，设置评估详情的任务ID
            AeaQuestionnaireBo aeaQuestionnaireBo = new AeaQuestionnaireBo();
            aeaQuestionnaireBo.setType(2);
            List<AeaQuestionnaireVo> questionnaireVoList = questionnaireService.queryList(aeaQuestionnaireBo);
            // 加入需要答题的问卷
            questionnaireVoList.forEach(questionnaireVo -> {
                AeaTaskEvaluateInfoBo evaluateInfoBo = new AeaTaskEvaluateInfoBo();
                evaluateInfoBo.setTaskId(taskId);
                evaluateInfoBo.setQuestionnaireId(questionnaireVo.getId());
                evaluateInfoBo.setStartTime(new Date());
                // 3. 创建评估详情
                Long infoId = evaluateInfoService.createEvaluateInfo(evaluateInfoBo);
                log.info("创建评估详情成功，详情ID: {}", infoId);
            });
            return taskId;
        } catch (Exception e) {
            log.error("创建评估任务流程失败", e);
            throw new RuntimeException("创建评估任务失败，请重新创建！");
        }
    }

    /**
     * 第二步：获取问卷目录结构
     * @param taskId 任务ID
     * @return 任务详情列表
     */
    @Override
    public List<AeaTaskEvaluateInfoVo> getAeaTaskEvaluateInfoList(Long taskId) {
        List<AeaTaskEvaluateInfoVo> infoVoList = new ArrayList<>();
        try {
            AeaTaskEvaluateInfoBo queryBo = new AeaTaskEvaluateInfoBo();
            queryBo.setTaskId(taskId);
            infoVoList = evaluateInfoService.queryList(queryBo);
            log.info("获取任务详情列表成功 · 任务ID: {}, 详情数量: {}", taskId, infoVoList.size());
        } catch (Exception e) {
            log.error("{}:问卷目录不存在！", taskId);
        }
        return infoVoList;
    }

    /**
     * 第三步：提交答案
     */
    @Override
    public Boolean submitAnswers(AeaTaskSubmitAnswerBo bo) {

        List<Long> answerIdList = new ArrayList<>();
        Long infoId = bo.getInfoId();
        Long taskId = bo.getTaskId();
        Long questionnaireId = bo.getQuestionnaireId(); // 问卷id
        List<AeaTaskEvaluateExecutionRecordBo> evaluateExecutionRecordBos = bo.getEvaluateExecutionRecordBos(); // 答卷内容
        log.info("【开始提交答案 · 任务ID: {}，详情ID: {}，问卷ID: {}，问题数量: {}】", taskId, infoId, questionnaireId, evaluateExecutionRecordBos.size());
        // 1. 查询模块目录下的题库数量
        AeaQuestionnaireQuestionBo questionnaireQuestionBo = new AeaQuestionnaireQuestionBo();
        questionnaireQuestionBo.setQuestionnaireId(questionnaireId);
        List<AeaQuestionnaireQuestionVo> aeaQuestionnaireQuestionVos = questionnaireQuestionService.queryList(questionnaireQuestionBo);
        // 2. 提交的答题数量与题库数量进行比对
        if (aeaQuestionnaireQuestionVos.size() != evaluateExecutionRecordBos.size()) {
            log.error("提交失败：提交答案为: {}, 题库数量为: {}", evaluateExecutionRecordBos.size(), aeaQuestionnaireQuestionVos.size());
            throw new RuntimeException("提交失败：请检查是否全部答完！");
        }
        evaluateExecutionRecordBos.forEach(record -> {
            answerIdList.add(record.getAnswerId());
        });
        List<AeaQuestionnaireAnswerVo> answerVos = aeaQuestionnaireAnswerMapper.selectVoByIds(answerIdList);
        // 3. 批量保存答题记录
        List<AeaTaskEvaluateExecutionRecord> evaluateExecutionRecordList = BeanUtil.copyToList(evaluateExecutionRecordBos, AeaTaskEvaluateExecutionRecord.class);
        // 得分初始化
        int score = 0;
        for (AeaQuestionnaireAnswerVo answerVo : answerVos) {
            // 得分累加
            score += answerVo.getScore();
        }
        log.info("【提交答案成功 · 任务ID: {}，详情ID: {}，问卷ID: {}，分数: {}】", taskId, infoId, questionnaireId, score);
        // 批量保存
        aeaTaskEvaluateExecutionRecordMapper.insertBatch(evaluateExecutionRecordList);
        // 更新分数，结束时间，评估状态
        AeaTaskEvaluateInfoBo aeaTaskEvaluateInfoBo = new AeaTaskEvaluateInfoBo();
        aeaTaskEvaluateInfoBo.setId(infoId);
        aeaTaskEvaluateInfoBo.setTotalScore(score);
        aeaTaskEvaluateInfoBo.setEndTime(new Date());
        aeaTaskEvaluateInfoBo.setStatus(TaskStatusEnum.COMPLETED.getCode());
        return evaluateInfoService.updateByBo(aeaTaskEvaluateInfoBo);
    }

    /**
     * 第四步：获取答题结果
     */
    @Override
    public Map<String, Object> getEvaluateReport(Long taskId, Long questionnaireId) {
        // 1. 获取答题结果
        LambdaQueryWrapper<AeaTaskEvaluateInfo> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(taskId != null, AeaTaskEvaluateInfo::getTaskId, taskId);
        queryWrapper.gt(AeaTaskEvaluateInfo::getTotalScore, (0));
        List<AeaTaskEvaluateInfoVo> info = aeaTaskEvaluateInfoMapper.selectVoList(queryWrapper);
        log.info("【获取答题结果成功 · 任务ID: {}，问卷ID: {}，结果数量: {}】", taskId, questionnaireId, info.size());
        if (info.isEmpty()) {
            log.error("【获取答题结果失败 · 任务ID: {}，问卷ID: {}】", taskId, questionnaireId);
            throw new RuntimeException("获取答题结果失败");
        }
        int total = 0;
        for (AeaTaskEvaluateInfoVo infoVo : info) {
            total += infoVo.getTotalScore();
        }
        Map<String, Object> result = new HashMap<>();
        // 获取模块列表详细
        result.put("records", info);
        // 总分
        result.put("total", total);
        if (total >= 90) {
            result.put("CapabilityLevel", AbilityLevelEnum.INTACT.getDescription());
        }
        if (total > 66 && total < 90) {
            result.put("CapabilityLevel", AbilityLevelEnum.MILD_DISABILITY.getDescription());
        }
        if (total > 46 && total <= 66) {
            result.put("CapabilityLevel", AbilityLevelEnum.MODERATE_DISABILITY.getDescription());
        }
        if (total > 30 && total <= 46) {
            result.put("CapabilityLevel", AbilityLevelEnum.SEVERE_DISABILITY.getDescription());
        }
        if (total <= 30) {
            result.put("CapabilityLevel", AbilityLevelEnum.FULL_DISABILITY.getDescription());
        }
        return result;
    }

    /**
     * 第五步：生成PDF报告
     * 完整实现PDF报告生成流程 - 重构版本
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String generateReportPdf(AeaEvaluateReportBo aeaEvaluateReportBo) {
        try {
            log.info("开始第五步：生成PDF报告，任务ID: {}", aeaEvaluateReportBo.getTaskId());

            // 1. 数据收集和验证
            Map<String, Object> collectedData = collectReportData(aeaEvaluateReportBo.getTaskId());

            // 2. 创建评估报告记录
            AeaEvaluateReport reportRecord = createReportRecord(aeaEvaluateReportBo, collectedData);

            // 3. 调用报告服务生成PDF - 使用整合后的方法
            String pdfUrl = evaluateReportService.generatePdfReportIntegrated(
                reportRecord.getId(),
                collectedData
            );

            if (pdfUrl == null || pdfUrl.trim().isEmpty()) {
                throw new RuntimeException("PDF报告生成失败，返回的URL为空");
            }

            log.info("PDF报告生成成功，报告ID: {}, PDF链接: {}", reportRecord.getId(), pdfUrl);
            return pdfUrl;
        } catch (Exception e) {
            log.error("生成PDF报告失败，任务ID: {}", aeaEvaluateReportBo.getTaskId(), e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage());
        }
    }

    /**
     * 收集报告生成所需的所有数据
     */
    private Map<String, Object> collectReportData(Long taskId) {
        Map<String, Object> collectedData = new LinkedHashMap<>();

        try {
            // 第一节：获取任务基本信息
            AeaTaskEvaluateVo taskEvaluate = aeaTaskEvaluateMapper.selectVoById(taskId);
            if (taskEvaluate == null) {
                throw new RuntimeException("任务不存在，任务ID: " + taskId);
            }
            collectedData.put("taskEvaluate", taskEvaluate);
            log.info("收集任务基本信息完成");

            // 第二节：获取基本信息表数据
            Map<String, Object> basicInfoData = collectBasicInfoData(taskId);
            collectedData.put("basicInfoData", basicInfoData);
            log.info("收集基本信息表数据完成");

            // 第三节：获取能力评估数据
            Map<String, Object> assessmentData = collectAssessmentData(taskId);
            collectedData.put("assessmentData", assessmentData);
            log.info("收集能力评估数据完成");

            // 4. 添加评估结果统计
            Map<String, Object> evaluationResults = calculateEvaluationResults(collectedData);
            collectedData.putAll(evaluationResults);

            // 5. 添加报告生成时间
            LocalDateTime now = LocalDateTime.now();
            collectedData.put("generateTime", now);
            collectedData.put("generateTimeStr", now.format(DateTimeFormatter.ofPattern("yyyy年MM月dd日 HH:mm:ss")));
            collectedData.put("reportDate", now.format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));

            log.info("报告数据收集完成，任务ID: {}, 数据项数量: {}", taskId, collectedData.size());
            return collectedData;
            
        } catch (Exception e) {
            log.error("第五步生成PDF报告失败，任务ID: {}", taskId, e);
            throw new RuntimeException("生成PDF报告失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据问卷问题id获取老年人能力评估表问卷问题所答信息
     */
    public void getQuestionnaireQuestionAnswers(List<AeaQuestionnaireVo> aeaQuestionnaireVoList,String name) {
        //获取老年人能力评估基本信息表->评估对象基本信息表所需填充数据
        LambdaQueryWrapper<AeaQuestionnaire> aeaQuestionnaireLambdaQueryWrapper = new LambdaQueryWrapper<>();
        //构建老年人能力评估基本信息表questionnaireIdChildrenIds
        List<AeaQuestionnaireVo> questionnaireIdChildrenIds = new ArrayList<>();
        for (AeaQuestionnaireVo vo : aeaQuestionnaireVoList){
            if (vo.getTitle().equals(name)){
                //根据id查询出子节点
                aeaQuestionnaireLambdaQueryWrapper.eq(AeaQuestionnaire::getParentId, vo.getId());
                questionnaireIdChildrenIds = aeaQuestionnaireMapper.selectVoList(aeaQuestionnaireLambdaQueryWrapper);
            }
        }
        //对questionnaireIdChildrenIds进行根据sort进行正序排序
        questionnaireIdChildrenIds = questionnaireIdChildrenIds.stream().sorted(Comparator.comparing(AeaQuestionnaireVo::getSort)).toList();
        //获取老年人能力评估基本信息表Children列表
        List<AeaTaskEvaluateExecutionRecordVo> aeaTaskEvaluateExecutionRecordVos;
        //根据问卷问题id获取->问卷问题所答信息
        LambdaQueryWrapper<AeaTaskEvaluateExecutionRecord> aeaTaskEvaluateExecutionRecordLambdaQueryWrapper = new LambdaQueryWrapper<>();
        for (AeaQuestionnaireVo questionnaireVo : questionnaireIdChildrenIds){
            aeaTaskEvaluateExecutionRecordLambdaQueryWrapper.eq(AeaTaskEvaluateExecutionRecord::getQuestionId, questionnaireVo.getId());
            aeaTaskEvaluateExecutionRecordVos = aeaTaskEvaluateExecutionRecordMapper.selectVoList(aeaTaskEvaluateExecutionRecordLambdaQueryWrapper);
            log.info("问卷问题所答信息: {}", aeaTaskEvaluateExecutionRecordVos);
            //TODO: 2023/6/5 PDF填充数据
        }
    }

    /**
     * 生成评估报告编号，格式为：AE + 当前日期时间 + 8位递增序列号
     * 示例：AE20250605143000000001
     */
    private String generateReportCode() {
        // 获取当前日期时间，格式为：yyyyMMddHHmmss
        String dateTime = DateUtils.dateTimeNow(FormatsType.YYYYMMDDHHMMSS);

        // 获取当日已生成的报告数量，用于生成递增序列号
        Long count = aeaEvaluateReportMapper.selectCount(new QueryWrapper<AeaEvaluateReport>()
                .like("code", "AE" + dateTime));

        // 计算下一个序列号，初始值为00000001
        long sequence = count == null ? 1 : count + 1;

        // 拼接评估报告编号
        return "AE" + dateTime + String.format("%08d", sequence);
    }

    /**
     * 获取问卷结构
     */
    @Override
    public AeaQuestionnaireVo getQuestionnaireStructure(Long questionnaireId) {
        try {
            AeaQuestionnaireVo questionnaire = questionnaireService.queryById(questionnaireId);
            if (questionnaire == null) {
                throw new RuntimeException("问卷不存在");
            }

            log.info("获取问卷结构成功，问卷ID: {}, 问卷名称: {}", questionnaireId, questionnaire.getTitle());
            return questionnaire;
        } catch (Exception e) {
            log.error("获取问卷结构失败，问卷ID: {}", questionnaireId, e);
            throw new RuntimeException("获取问卷结构失败: " + e.getMessage());
        }
    }

    /**
     * 获取问卷问题列表
     */
    @Override
    public List<AeaQuestionnaireQuestionVo> getQuestionnaireQuestions(Long questionnaireId) {
        try {
            AeaQuestionnaireQuestionBo queryBo = new AeaQuestionnaireQuestionBo();
            queryBo.setQuestionnaireId(questionnaireId);

            List<AeaQuestionnaireQuestionVo> questions = questionnaireQuestionService.queryList(queryBo);

            if (CollUtil.isEmpty(questions)) {
                log.warn("问卷没有问题，问卷ID: {}", questionnaireId);
                throw new RuntimeException("问卷没有配置问题");
            }

            log.info("获取问卷问题成功，问卷ID: {}, 问题数量: {}", questionnaireId, questions.size());
            return questions;
        } catch (Exception e) {
            log.error("获取问卷问题失败，问卷ID: {}", questionnaireId, e);
            throw new RuntimeException("获取问卷问题失败: " + e.getMessage());
        }
    }


    /**
     * 获取报告下载链接
     */
    @Override
    public String getReportDownloadUrl(Long taskId) {
        try {
            // 根据任务ID查询报告
            AeaEvaluateReportVo report = evaluateReportService.queryByTaskId(taskId);
            if (report == null) {
                throw new RuntimeException("评估报告不存在");
            }

            // 获取下载链接
            String downloadUrl = evaluateReportService.getReportDownloadUrl(report.getId());
            log.info("获取报告下载链接成功，任务ID: {}, 报告ID: {}, 下载链接: {}",
                    taskId, report.getId(), downloadUrl);

            return downloadUrl;
        } catch (Exception e) {
            log.error("获取报告下载链接失败，任务ID: {}", taskId, e);
            throw new RuntimeException("获取报告下载链接失败: " + e.getMessage());
        }
    }

    /**
     * 重新生成PDF报告
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String regeneratePdfReport(Long taskId) {
        try {
            // 根据任务ID查询报告
            AeaEvaluateReportVo report = evaluateReportService.queryByTaskId(taskId);
            if (report == null) {
                throw new RuntimeException("评估报告不存在");
            }

            // 重新生成PDF报告 - 使用整合后的方法
            Map<String, Object> reportData = collectReportData(taskId);
            String pdfUrl = evaluateReportService.generatePdfReportIntegrated(report.getId(), reportData);
            log.info("重新生成PDF报告成功，任务ID: {}, 报告ID: {}, PDF链接: {}",
                    taskId, report.getId(), pdfUrl);

            return pdfUrl;
        } catch (Exception e) {
            log.error("重新生成PDF报告失败，任务ID: {}", taskId, e);
            throw new RuntimeException("重新生成PDF报告失败: " + e.getMessage());
        }
    }


    /*=========================== 新增的辅助方法 ===========================*/

    /**
     * 收集基本信息表数据
     */
    private Map<String, Object> collectBasicInfoData(Long taskId) {
        Map<String, Object> basicInfoData = new HashMap<>();

        try {
            // 获取问卷列表
            List<AeaQuestionnaireVo> questionnaireList = aeaQuestionnaireMapper.selectVoList();

            // 获取基本信息表的子问卷
            List<AeaQuestionnaireVo> basicInfoQuestionnaires = new ArrayList<>();
            for (AeaQuestionnaireVo questionnaire : questionnaireList) {
                if ("老年人能力评估基本信息表".equals(questionnaire.getTitle())) {
                    // 查询子问卷
                    LambdaQueryWrapper<AeaQuestionnaire> childQuery = new LambdaQueryWrapper<>();
                    childQuery.eq(AeaQuestionnaire::getParentId, questionnaire.getId());
                    basicInfoQuestionnaires = aeaQuestionnaireMapper.selectVoList(childQuery);
                    break;
                }
            }

            // 按sort排序
            basicInfoQuestionnaires = basicInfoQuestionnaires.stream()
                .sorted(Comparator.comparing(AeaQuestionnaireVo::getSort))
                .collect(Collectors.toList());

            // 收集每个基本信息子表的执行记录
            for (AeaQuestionnaireVo questionnaire : basicInfoQuestionnaires) {
                LambdaQueryWrapper<AeaTaskEvaluateExecutionRecord> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(AeaTaskEvaluateExecutionRecord::getTaskId, taskId)
                           .eq(AeaTaskEvaluateExecutionRecord::getQuestionId, questionnaire.getId());

                List<AeaTaskEvaluateExecutionRecordVo> records =
                    aeaTaskEvaluateExecutionRecordMapper.selectVoList(queryWrapper);

                basicInfoData.put(questionnaire.getTitle(), records);
                log.info("收集基本信息数据: {}, 记录数: {}", questionnaire.getTitle(), records.size());
            }

            return basicInfoData;

        } catch (Exception e) {
            log.error("收集基本信息数据失败，任务ID: {}", taskId, e);
            throw new RuntimeException("收集基本信息数据失败: " + e.getMessage());
        }
    }

    /**
     * 收集能力评估数据
     */
    private Map<String, Object> collectAssessmentData(Long taskId) {
        Map<String, Object> assessmentData = new HashMap<>();

        try {
            // 获取评估任务模块记录
            LambdaQueryWrapper<AeaTaskEvaluateInfo> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(AeaTaskEvaluateInfo::getTaskId, taskId);
            List<AeaTaskEvaluateInfoVo> evaluateInfoList = aeaTaskEvaluateInfoMapper.selectVoList(queryWrapper);

            // 收集每个评估模块的执行记录
            for (AeaTaskEvaluateInfoVo evaluateInfo : evaluateInfoList) {
                LambdaQueryWrapper<AeaTaskEvaluateExecutionRecord> recordQuery = new LambdaQueryWrapper<>();
                recordQuery.eq(AeaTaskEvaluateExecutionRecord::getInfoId, evaluateInfo.getId());

                List<AeaTaskEvaluateExecutionRecordVo> records =
                    aeaTaskEvaluateExecutionRecordMapper.selectVoList(recordQuery);

                assessmentData.put(evaluateInfo.getQuestionnaireId().toString(), records);
                log.info("收集评估数据: {}, 记录数: {}", evaluateInfo.getQuestionnaireId(), records.size());
            }

            return assessmentData;

        } catch (Exception e) {
            log.error("收集评估数据失败，任务ID: {}", taskId, e);
            throw new RuntimeException("收集评估数据失败: " + e.getMessage());
        }
    }


    /**
     * 创建评估报告记录
     */
    private AeaEvaluateReport createReportRecord(AeaEvaluateReportBo reportBo, Map<String, Object> collectedData) {
        try {
            // 计算各维度分数
            Map<String, Integer> dimensionScores = calculateDimensionScores(collectedData);

            // 创建报告记录
            AeaEvaluateReport report = BeanUtil.copyProperties(reportBo, AeaEvaluateReport.class);
            report.setCode(generateReportCode());
            report.setCreateTime(new Date());
            report.setUpdateTime(new Date());

            // 设置各维度分数
            report.setSelfScore(dimensionScores.get("selfCare"));
            report.setBaseScore(dimensionScores.get("basicMobility"));
            report.setMentionScore(dimensionScores.get("mentalState"));
            report.setFeelScore(dimensionScores.get("perceptionSocial"));
            report.setTotalScore(dimensionScores.get("total"));

            // 计算评估等级
            Integer level = calculateAssessmentLevel(dimensionScores.get("total"));
            report.setFirstLevel(level);
            report.setFinalLevel(level);

            // 保存报告记录
            int insertResult = aeaEvaluateReportMapper.insert(report);
            if (insertResult <= 0) {
                throw new RuntimeException("保存评估报告失败");
            }

            log.info("创建评估报告记录成功，报告ID: {}, 报告编码: {}", report.getId(), report.getCode());
            return report;

        } catch (Exception e) {
            log.error("创建评估报告记录失败", e);
            throw new RuntimeException("创建评估报告记录失败: " + e.getMessage());
        }
    }

    /**
     * 计算评估结果
     */
    private Map<String, Object> calculateEvaluationResults(Map<String, Object> collectedData) {
        Map<String, Object> results = new HashMap<>();

        try {
            // 获取评估数据
            Map<String, Object> assessmentData = (Map<String, Object>) collectedData.get("assessmentData");
            if (assessmentData == null) {
                log.warn("评估数据为空，使用默认分数");
                results.put("selfCareScore", 0);
                results.put("basicMobilityScore", 0);
                results.put("mentalStateScore", 0);
                results.put("perceptionSocialScore", 0);
                results.put("totalScore", 0);
                results.put("assessmentLevel", 1);
                return results;
            }

            // 计算各维度分数
            int selfCareScore = calculateDimensionScore(assessmentData, "自理能力评估");
            int basicMobilityScore = calculateDimensionScore(assessmentData, "基础运动能力评估");
            int mentalStateScore = calculateDimensionScore(assessmentData, "精神状态评估");
            int perceptionSocialScore = calculateDimensionScore(assessmentData, "感知觉与社会参与评估");

            // 计算总分
            int totalScore = selfCareScore + basicMobilityScore + mentalStateScore + perceptionSocialScore;

            // 计算评估等级
            int assessmentLevel = calculateAssessmentLevel(totalScore);

            // 保存结果
            results.put("selfCareScore", selfCareScore);
            results.put("basicMobilityScore", basicMobilityScore);
            results.put("mentalStateScore", mentalStateScore);
            results.put("perceptionSocialScore", perceptionSocialScore);
            results.put("totalScore", totalScore);
            results.put("assessmentLevel", assessmentLevel);

            log.info("评估结果计算完成 - 自理能力: {}, 基础运动: {}, 精神状态: {}, 感知觉社会参与: {}, 总分: {}, 等级: {}",
                selfCareScore, basicMobilityScore, mentalStateScore, perceptionSocialScore, totalScore, assessmentLevel);

            return results;

        } catch (Exception e) {
            log.error("计算评估结果失败", e);
            throw new RuntimeException("计算评估结果失败: " + e.getMessage());
        }
    }

    /**
     * 计算单个维度分数
     */
    private int calculateDimensionScore(Map<String, Object> assessmentData, String dimensionName) {
        try {
            List<AeaTaskEvaluateExecutionRecordVo> records =
                (List<AeaTaskEvaluateExecutionRecordVo>) assessmentData.get(dimensionName);

            if (records == null || records.isEmpty()) {
                log.warn("维度 {} 的评估记录为空", dimensionName);
                return 0;
            }

            // 计算该维度的总分
            int totalScore = records.stream()
                .mapToInt(record -> record.getScore() != null ? record.getScore() : 0)
                .sum();

            log.debug("维度 {} 分数计算完成: {}", dimensionName, totalScore);
            return totalScore;

        } catch (Exception e) {
            log.error("计算维度 {} 分数失败", dimensionName, e);
            return 0;
        }
    }

    /**
     * 计算各维度分数 - 兼容原有方法
     */
    private Map<String, Integer> calculateDimensionScores(Map<String, Object> collectedData) {
        Map<String, Integer> scores = new HashMap<>();

        try {
            Map<String, Object> evaluationResults = calculateEvaluationResults(collectedData);

            scores.put("selfCare", (Integer) evaluationResults.get("selfCareScore"));
            scores.put("basicMobility", (Integer) evaluationResults.get("basicMobilityScore"));
            scores.put("mentalState", (Integer) evaluationResults.get("mentalStateScore"));
            scores.put("perceptionSocial", (Integer) evaluationResults.get("perceptionSocialScore"));
            scores.put("total", (Integer) evaluationResults.get("totalScore"));

            return scores;

        } catch (Exception e) {
            log.error("计算维度分数失败", e);
            throw new RuntimeException("计算维度分数失败: " + e.getMessage());
        }
    }

    /**
     * 计算评估等级
     */
    private Integer calculateAssessmentLevel(Integer totalScore) {
        if (totalScore >= 90) {
            return 1; // 能力完好
        } else if (totalScore > 66) {
            return 2; // 轻度失能
        } else if (totalScore > 46) {
            return 3; // 中度失能
        } else if (totalScore > 30) {
            return 4; // 重度失能
        } else {
            return 5; // 极重度失能
        }
    }
}