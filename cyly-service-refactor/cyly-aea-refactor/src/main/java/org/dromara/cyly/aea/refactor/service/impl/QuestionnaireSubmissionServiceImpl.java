package org.dromara.cyly.aea.refactor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.utils.MapstructUtils;
import org.dromara.common.satoken.utils.LoginHelper;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReport;
import org.dromara.cyly.aea.refactor.domain.AeaTask;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateExecutionRecord;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateInfo;
import org.dromara.cyly.aea.refactor.domain.bo.QuestionnaireSubmissionBo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireQuestionVo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireVo;
import org.dromara.cyly.aea.refactor.mapper.AeaEvaluateReportMapper;
import org.dromara.cyly.aea.refactor.mapper.AeaTaskEvaluateExecutionRecordMapper;
import org.dromara.cyly.aea.refactor.mapper.AeaTaskEvaluateInfoMapper;
import org.dromara.cyly.aea.refactor.mapper.AeaTaskMapper;
import org.dromara.cyly.aea.refactor.service.IAeaQuestionnaireQuestionService;
import org.dromara.cyly.aea.refactor.service.IAeaQuestionnaireService;
import org.dromara.cyly.aea.refactor.service.IQuestionnaireSubmissionService;
import org.dromara.cyly.aea.refactor.service.IScoreCalculationService;
import org.dromara.cyly.aea.refactor.templates.GBReportTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 问卷提交与报告生成服务实现类
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class QuestionnaireSubmissionServiceImpl implements IQuestionnaireSubmissionService {

    private final AeaTaskMapper taskMapper;
    private final AeaTaskEvaluateInfoMapper taskEvaluateInfoMapper;
    private final AeaTaskEvaluateExecutionRecordMapper taskEvaluateExecutionRecordMapper;
    private final AeaEvaluateReportMapper evaluateReportMapper;
    private final IAeaQuestionnaireService questionnaireService; // 用于获取问卷信息
    private final IAeaQuestionnaireQuestionService questionnaireQuestionService; // 用于获取问题和答案信息
    private final IScoreCalculationService scoreCalculationService;
    private final GBReportTemplate gbReportTemplate; // PDF生成模板
    private final AeaEvaluateReportServiceImpl aeaEvaluateReportServiceImpl; //复用部分逻辑

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AeaEvaluateReportVo submitQuestionnaireAndGenerateReport(QuestionnaireSubmissionBo bo) throws Exception {
        log.info("开始处理问卷提交与报告生成流程, elderId: {}, questionnaireId: {}", bo.getElderId(), bo.getQuestionnaireId());

        // 1. 创建任务 (AeaTask)
        AeaTask task = createTask(bo);
        taskMapper.insert(task);
        log.info("任务创建成功, taskId: {}", task.getId());

        // 2. 创建评估详情 (AeaTaskEvaluateInfo)
        AeaTaskEvaluateInfo evaluateInfo = createEvaluateInfo(task, bo);
        taskEvaluateInfoMapper.insert(evaluateInfo);
        log.info("评估详情创建成功, evaluateInfoId: {}", evaluateInfo.getId());

        // 3. 批量保存答题记录 (AeaTaskEvaluateExecutionRecord)
        if (CollectionUtils.isEmpty(bo.getAnswers())) {
            log.warn("答案列表为空, questionnaireId: {}", bo.getQuestionnaireId());
            // 根据业务需求，这里可以抛出异常或执行其他逻辑
        }
        batchSaveExecutionRecords(task, evaluateInfo, bo.getAnswers());
        log.info("答题记录保存成功, 数量: {}", bo.getAnswers() != null ? bo.getAnswers().size() : 0);

        // 4. 计算分数和等级 (AeaEvaluateReport的前置数据准备)
        // 注意：分数计算和等级评定逻辑在 AeaEvaluateReportServiceImpl.createEvaluateReport 中实现
        // 这里我们先创建一个初始的 AeaEvaluateReport 对象，然后调用其内部逻辑完成分数计算和等级评定

        AeaEvaluateReport report = new AeaEvaluateReport();
        report.setTaskId(task.getId());
        report.setElderId(bo.getElderId());
        report.setAssessorEvaluateUserId(bo.getAssessorId());
        // report.setDeputyEvaluateUserId(); // 副评估员ID，如果BO中没有，需要确定来源
        report.setLocation(bo.getLocation());
        // report.setReasonCode(); // 评估原因，需要确定来源或默认值
        report.setAssessorSign(bo.getAssessorSign());
        report.setDeputySign(bo.getDeputySign());
        report.setInformationProviderSign(bo.getFamilySign());
        report.setCreateTime(new Date());
        report.setCreateBy(LoginHelper.getUserId());

        // 调用 AeaEvaluateReportServiceImpl 的方法来创建报告记录，该方法内部会计算分数和等级
        AeaEvaluateReportVo createdReportVo = aeaEvaluateReportServiceImpl.createEvaluateReport(report);
        log.info("评估报告记录创建成功, reportId: {}", createdReportVo.getId());

        // 5. 生成PDF报告
        // 使用 AeaEvaluateReportServiceImpl 中的 generatePdfReport 方法
        // 它会根据 reportId 找到报告，然后生成PDF
        AeaEvaluateReportVo finalReportVo = aeaEvaluateReportServiceImpl.generatePdfReport(createdReportVo.getId());
        log.info("PDF报告生成并更新URL成功, reportUrl: {}", finalReportVo.getReportUrl());

        return finalReportVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public AeaEvaluateReportVo generatePdfReportByTaskId(Long taskId, String assessorSign, String deputySign, String familySign) throws Exception {
        log.info("开始根据任务ID生成PDF报告, taskId: {}", taskId);
        AeaEvaluateReport report = evaluateReportMapper.selectOne(new LambdaQueryWrapper<AeaEvaluateReport>().eq(AeaEvaluateReport::getTaskId, taskId));
        if (report == null) {
            log.error("未找到任务ID对应的评估报告: {}", taskId);
            throw new RuntimeException("未找到任务ID对应的评估报告: " + taskId);
        }

        // 更新签名信息
        boolean needUpdate = false;
        if (assessorSign != null && !assessorSign.isEmpty() && !assessorSign.equals(report.getAssessorSign())) {
            report.setAssessorSign(assessorSign);
            needUpdate = true;
        }
        if (deputySign != null && !deputySign.isEmpty() && !deputySign.equals(report.getDeputySign())) {
            report.setDeputySign(deputySign);
            needUpdate = true;
        }
        if (familySign != null && !familySign.isEmpty() && !familySign.equals(report.getInformationProviderSign())) {
            report.setInformationProviderSign(familySign);
            needUpdate = true;
        }

        if (needUpdate) {
            evaluateReportMapper.updateById(report);
            log.info("评估报告签名信息更新成功, reportId: {}", report.getId());
        }

        // 调用现有方法生成PDF
        AeaEvaluateReportVo finalReportVo = aeaEvaluateReportServiceImpl.generatePdfReport(report.getId());
        log.info("PDF报告生成并更新URL成功 (基于任务ID), reportUrl: {}", finalReportVo.getReportUrl());
        return finalReportVo;
    }


    private AeaTask createTask(QuestionnaireSubmissionBo bo) {
        AeaTask task = new AeaTask();
        task.setCreateDept(LoginHelper.getDeptId()); // 机构ID，从登录信息获取
        task.setTaskType(1); // 1-评估任务
        // task.setExpectedStartTime(); // 期望开始时间，根据业务需求设定
        // task.setExpectedEndTime(); // 期望完成时间，根据业务需求设定
        task.setStartTime(new Date()); // 实际开始时间
        task.setStatus(0); // 0-待执行 (后续会变为执行中，然后完成)
        task.setAuditStatus(0); // 0-无审核 (或根据业务调整)
        task.setIsDel(0);
        task.setCreateBy(LoginHelper.getUserId());
        task.setCreateTime(new Date());
        // task.setTaskName(); // 任务名称，可以从问卷名称或自定义
        AeaQuestionnaireVo questionnaireVo = questionnaireService.queryById(bo.getQuestionnaireId());
        if (questionnaireVo != null) {
            task.setTaskName(questionnaireVo.getName() + "-评估任务");
        }
        return task;
    }

    private AeaTaskEvaluateInfo createEvaluateInfo(AeaTask task, QuestionnaireSubmissionBo bo) {
        AeaTaskEvaluateInfo evaluateInfo = new AeaTaskEvaluateInfo();
        evaluateInfo.setTaskId(task.getId());
        evaluateInfo.setQuestionnaireId(bo.getQuestionnaireId());
        evaluateInfo.setAssessorEvaluateUserId(bo.getAssessorId());
        // evaluateInfo.setViceEvaluateUserId(); // 副评估员ID，如果BO中没有，需要确定来源
        evaluateInfo.setElderId(bo.getElderId());
        // evaluateInfo.setTotalScore(); // 总分，后续计算
        evaluateInfo.setStatus(0); // 0-待开始 (后续会变为执行中，然后完成)
        evaluateInfo.setStartTime(new Date());
        evaluateInfo.setCreateBy(LoginHelper.getUserId());
        evaluateInfo.setCreateTime(new Date());
        return evaluateInfo;
    }

    private void batchSaveExecutionRecords(AeaTask task, AeaTaskEvaluateInfo evaluateInfo, List<QuestionnaireSubmissionBo.AnswerBo> answers) {
        if (CollectionUtils.isEmpty(answers)) {
            return;
        }
        List<AeaTaskEvaluateExecutionRecord> records = new ArrayList<>();
        for (QuestionnaireSubmissionBo.AnswerBo answerBo : answers) {
            AeaTaskEvaluateExecutionRecord record = new AeaTaskEvaluateExecutionRecord();
            record.setInfoId(evaluateInfo.getId());
            record.setTaskId(task.getId());
            record.setQuestionId(answerBo.getQuestionId());
            record.setAnswerId(answerBo.getAnswerId());
            record.setContent(answerBo.getContent());
            record.setScore(answerBo.getScore()); // 如果前端已计算或有预定义分数
            // record.setBatchNum(); // 批次号，如果需要
            record.setEvaluateUserId(evaluateInfo.getAssessorEvaluateUserId()); // 执行评估的用户id
            record.setCreateBy(LoginHelper.getUserId());
            record.setCreateTime(new Date());
            records.add(record);
        }
        taskEvaluateExecutionRecordMapper.insertBatch(records);
    }
}