package org.dromara.cyly.aea.refactor.controller;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.common.core.domain.R;
import org.dromara.common.core.validate.AddGroup;
import org.dromara.common.log.annotation.Log;
import org.dromara.common.log.enums.BusinessType;
import org.dromara.common.web.core.BaseController;
import org.dromara.cyly.aea.refactor.domain.bo.QuestionnaireSubmissionBo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVo;
import org.dromara.cyly.aea.refactor.service.IQuestionnaireSubmissionService;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 问卷提交与报告生成
 *
 * <AUTHOR>
 */
@Validated
@Slf4j
@RequiredArgsConstructor
@RestController
@RequestMapping("/aea-refactor/questionnaire-submission")
public class QuestionnaireSubmissionController extends BaseController {

    private final IQuestionnaireSubmissionService questionnaireSubmissionService;

    /**
     * 提交问卷答案并直接生成报告
     */
    @Log(title = "问卷提交与报告生成", businessType = BusinessType.INSERT)
    @PostMapping("/submit-and-generate")
    public R<AeaEvaluateReportVo> submitAndGenerateReport(@Validated(AddGroup.class) @RequestBody QuestionnaireSubmissionBo bo) {
        log.info("接收到问卷提交并生成报告请求: {}", bo);
        try {
            AeaEvaluateReportVo reportVo = questionnaireSubmissionService.submitQuestionnaireAndGenerateReport(bo);
            log.info("问卷提交并生成报告成功, 报告信息: {}", reportVo);
            return R.ok(reportVo);
        } catch (Exception e) {
            log.error("问卷提交并生成报告失败", e);
            return R.fail("问卷提交并生成报告失败: " + e.getMessage());
        }
    }

    /**
     * 根据已提交的任务ID生成PDF报告 (此接口用于已存在任务和评估记录的情况)
     * 注意：此接口主要用于演示或特定场景，推荐使用 submitAndGenerateReport 一体化接口
     */
    @Log(title = "根据任务ID生成报告", businessType = BusinessType.UPDATE) // BusinessType 可以根据实际情况调整
    @PostMapping("/generate-report-by-task")
    public R<AeaEvaluateReportVo> generateReportByTaskId(@RequestBody Map<String, Object> params) {
        Long taskId = Long.parseLong(params.getOrDefault("taskId", "0").toString());
        String assessorSign = params.getOrDefault("assessorSign", "").toString();
        String deputySign = params.getOrDefault("deputySign", "").toString();
        String familySign = params.getOrDefault("familySign", "").toString();

        log.info("接收到根据任务ID生成报告请求, taskId: {}, assessorSign: {}, deputySign: {}, familySign: {}", taskId, assessorSign, deputySign, familySign);
        if (taskId == 0) {
            return R.fail("任务ID不能为空");
        }
        try {
            AeaEvaluateReportVo reportVo = questionnaireSubmissionService.generatePdfReportByTaskId(taskId, assessorSign, deputySign, familySign);
            log.info("根据任务ID生成报告成功, 报告信息: {}", reportVo);
            return R.ok(reportVo);
        } catch (Exception e) {
            log.error("根据任务ID生成报告失败", e);
            return R.fail("根据任务ID生成报告失败: " + e.getMessage());
        }
    }
}