package org.dromara.cyly.aea.refactor.service;

import org.dromara.cyly.aea.refactor.domain.bo.QuestionnaireSubmissionBo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaEvaluateReportVo;

/**
 * 问卷提交与报告生成服务接口
 */
public interface IQuestionnaireSubmissionService {

    /**
     * 提交问卷答案并直接生成报告
     *
     * @param bo 问卷提交业务对象
     * @return 评估报告视图对象，包含报告URL
     * @throws Exception 生成报告过程中可能发生的异常
     */
    AeaEvaluateReportVo submitQuestionnaireAndGenerateReport(QuestionnaireSubmissionBo bo) throws Exception;

    /**
     * 根据已存在的任务ID生成PDF报告，并更新签名信息
     *
     * @param taskId       任务ID
     * @param assessorSign 主评估员签名
     * @param deputySign   副评估员签名
     * @param familySign   家属/信息提供者签名
     * @return 评估报告视图对象，包含报告URL
     * @throws Exception 生成报告过程中可能发生的异常
     */
    AeaEvaluateReportVo generatePdfReportByTaskId(Long taskId, String assessorSign, String deputySign, String familySign) throws Exception;

}