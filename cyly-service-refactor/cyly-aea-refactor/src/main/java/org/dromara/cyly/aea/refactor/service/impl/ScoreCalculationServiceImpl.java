package org.dromara.cyly.aea.refactor.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.dromara.cyly.aea.refactor.domain.AeaEvaluateReport;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateExecutionRecord;
import org.dromara.cyly.aea.refactor.domain.AeaTaskEvaluateInfo;
import org.dromara.cyly.aea.refactor.domain.bo.ScoreResultBo;
import org.dromara.cyly.aea.refactor.domain.vo.AeaQuestionnaireQuestionVo;
import org.dromara.cyly.aea.refactor.mapper.AeaTaskEvaluateExecutionRecordMapper;
import org.dromara.cyly.aea.refactor.mapper.AeaTaskEvaluateInfoMapper;
import org.dromara.cyly.aea.refactor.service.IAeaQuestionnaireQuestionService;
import org.dromara.cyly.aea.refactor.service.IScoreCalculationService;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 分数计算服务实现类
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class ScoreCalculationServiceImpl implements IScoreCalculationService {

    private final AeaTaskEvaluateExecutionRecordMapper executionRecordMapper;
    private final AeaTaskEvaluateInfoMapper taskEvaluateInfoMapper; 
    private final IAeaQuestionnaireQuestionService questionnaireQuestionService; // 用于获取问题详情，特别是问题分类

    @Override
    public AeaEvaluateReport calculateScoresAndLevel(Long evaluateInfoId, AeaEvaluateReport report) {
        log.info("开始为评估详情ID: {} 计算分数和等级", evaluateInfoId);
        AeaTaskEvaluateInfo evaluateInfo = taskEvaluateInfoMapper.selectById(evaluateInfoId);
        if (evaluateInfo == null) {
            log.error("未找到评估详情信息: {}", evaluateInfoId);
            throw new RuntimeException("未找到评估详情信息: " + evaluateInfoId);
        }

        List<AeaTaskEvaluateExecutionRecord> records = executionRecordMapper.selectList(
            new LambdaQueryWrapper<AeaTaskEvaluateExecutionRecord>().eq(AeaTaskEvaluateExecutionRecord::getInfoId, evaluateInfoId)
        );

        if (CollectionUtils.isEmpty(records)) {
            log.warn("评估详情ID: {} 没有对应的答题记录", evaluateInfoId);
            // 设置默认分数和等级或根据业务抛出异常
            report.setSelfScore(0);
            report.setBaseScore(0);
            report.setMentionScore(0);
            report.setFeelScore(0);
            report.setTotalScore(0);
            report.setFirstLevel(1); // 默认为能力完好或特定值
            report.setFinalLevel(1);
            return report;
        }

        // 获取所有相关问题的分类信息
        List<Long> questionIds = records.stream().map(AeaTaskEvaluateExecutionRecord::getQuestionId).distinct().collect(Collectors.toList());
        Map<Long, AeaQuestionnaireQuestionVo> questionMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(questionIds)){
            List<AeaQuestionnaireQuestionVo> questions = questionnaireQuestionService.selectListByIds(questionIds);
            if(!CollectionUtils.isEmpty(questions)){
                 questionMap.putAll(questions.stream().collect(Collectors.toMap(AeaQuestionnaireQuestionVo::getId, q -> q)));
            }
        }
       

        int selfScore = 0;
        int baseScore = 0;
        int mentionScore = 0;
        int feelScore = 0;

        for (AeaTaskEvaluateExecutionRecord record : records) {
            AeaQuestionnaireQuestionVo question = questionMap.get(record.getQuestionId());
            if (question == null || !StringUtils.hasText(question.getAssessmentCategory())) {
                log.warn("问题ID: {} 没有找到或没有评估分类信息，跳过计分", record.getQuestionId());
                continue;
            }
            // 假设 question.getAssessmentCategory() 返回的是 "B.1", "B.2" 等
            String categoryPrefix = getCategoryPrefix(question.getAssessmentCategory());
            Integer score = record.getScore() == null ? 0 : record.getScore();

            switch (categoryPrefix) {
                case "B.1": // 自理能力
                    selfScore += score;
                    break;
                case "B.2": // 基础运动能力
                    baseScore += score;
                    break;
                case "B.3": // 精神状态
                    mentionScore += score;
                    break;
                case "B.4": // 感知觉与社会参与
                    feelScore += score;
                    break;
                default:
                    log.warn("未知的评估分类前缀: {} (来自 {}), 问题ID: {}", categoryPrefix, question.getAssessmentCategory(), record.getQuestionId());
                    break;
            }
        }

        report.setSelfScore(selfScore);
        report.setBaseScore(baseScore);
        report.setMentionScore(mentionScore);
        report.setFeelScore(feelScore);

        int totalScore = selfScore + baseScore + mentionScore + feelScore;
        report.setTotalScore(totalScore);

        Integer firstLevel = calculateFirstLevel(totalScore);
        report.setFirstLevel(firstLevel);

        // 最终等级，如果调整依据为空，则与初步等级一致
        Integer finalLevel = calculateFinalLevel(firstLevel, report.getAdjustmentBasis());
        report.setFinalLevel(finalLevel);

        log.info("评估详情ID: {} 分数计算完成. 自理: {}, 基础运动: {}, 精神: {}, 感知社交: {}, 总分: {}, 初步等级: {}, 最终等级: {}",
            evaluateInfoId, selfScore, baseScore, mentionScore, feelScore, totalScore, firstLevel, finalLevel);
        return report;
    }

    private String getCategoryPrefix(String assessmentCategory) {
        if (assessmentCategory == null) return "";
        if (assessmentCategory.startsWith("B.1")) return "B.1";
        if (assessmentCategory.startsWith("B.2")) return "B.2";
        if (assessmentCategory.startsWith("B.3")) return "B.3";
        if (assessmentCategory.startsWith("B.4")) return "B.4";
        return assessmentCategory; // 或者返回空字符串或特定标记
    }

    @Override
    public Map<String, Integer> calculateCategoryScores(Long evaluateInfoId) {
        // 此方法可以类似地实现，但返回的是一个Map，键为如 "B.1", "B.2" 等，值为对应分数
        // 主要用于如果需要在其他地方单独展示各维度分数
        log.warn("calculateCategoryScores 方法尚未完全实现详细逻辑，仅为示例结构");
        return new HashMap<>(); // 示例返回
    }

    @Override
    public Integer calculateFirstLevel(Integer totalScore) {
        // 根据实际业务规则定义总分到等级的映射
        // 示例规则 (需要与 AeaEvaluateReportServiceImpl 中的 convertLevelToInt 一致或进行调整):
        // 0-能力完好, 1-轻度受损, 2-中度受损, 3-重度受损 (这里的数字可能与文档中的1-5不完全对应，需要核对)
        // 假设文档中的 1-能力完好, 2-轻度受损, 3-中度受损, 4-重度受损, 5-完全丧失
        // 需要一个明确的总分区间与这五个等级的对应关系
        // 例如（纯假设，需要业务确认）：
        if (totalScore == null) return 1; // 默认能力完好
        if (totalScore >= 90) return 1; // 能力完好
        if (totalScore >= 70) return 2; // 轻度受损
        if (totalScore >= 50) return 3; // 中度受损
        if (totalScore >= 30) return 4; // 重度受损
        return 5; // 完全丧失
    }

    @Override
    public Integer calculateFinalLevel(Integer firstLevel, String adjustmentBasis) {
        // 如果没有调整依据，最终等级等于初步等级
        if (!StringUtils.hasText(adjustmentBasis)) {
            return firstLevel;
        }
        // 如果有调整依据，则根据业务规则进行调整
        // 这里的逻辑可能比较复杂，例如：如果调整依据包含特定关键词，则等级上升或下降
        // 目前简单处理：如果存在调整依据，则最终等级与初步等级一致，实际业务中可能需要更复杂的判断
        log.info("存在调整依据: '{}', 但当前最终等级计算逻辑未实现基于依据的调整，最终等级同初步等级.", adjustmentBasis);
        return firstLevel; 
    }
}