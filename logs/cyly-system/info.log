2025-06-08 05:57:19 [DubboMetadataReportTimer-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBB<PERSON>] start to publish all metadata., dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteIpBanService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteIpBanService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=banNetwork,isNetworkBanned, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102415}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteIpBanService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=banNetwork, parameterTypes=[java.lang.String, int], returnType=boolean], MethodDefinition [name=isNetworkBanned, parameterTypes=[java.lang.String], returnType=boolean]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteDictService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteDictService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=selectDictDataByType, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102435}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteDictService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=selectDictDataByType, parameterTypes=[java.lang.String], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteDictDataVo>]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteClientService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteClientService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=queryByAppClientId,queryByClientId, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102456}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteClientService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=queryByAppClientId, parameterTypes=[java.lang.String], returnType=org.dromara.system.api.domain.vo.RemoteClientVo], MethodDefinition [name=queryByClientId, parameterTypes=[java.lang.String], returnType=org.dromara.system.api.domain.vo.RemoteClientVo]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemotePostService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemotePostService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=selectPostList,selectPostListByUserId, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102580}} ServiceDefinition [canonicalName=org.dromara.system.api.RemotePostService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=selectPostListByUserId, parameterTypes=[java.lang.Long], returnType=java.lang.Object], MethodDefinition [name=selectPostList, parameterTypes=[org.dromara.system.api.domain.bo.RemotePostBo], returnType=java.lang.Object]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteDictAreaService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteDictAreaService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=getAreaFullName,getAreaId,getAreaLevel,getAreaName, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102493}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteDictAreaService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=getAreaId, parameterTypes=[java.lang.String], returnType=java.lang.String], MethodDefinition [name=getAreaName, parameterTypes=[java.lang.String], returnType=java.lang.String], MethodDefinition [name=getAreaFullName, parameterTypes=[java.lang.String], returnType=java.lang.String], MethodDefinition [name=getAreaLevel, parameterTypes=[java.lang.String], returnType=java.lang.Integer]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteAppLogService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.system.api.RemoteAppLogService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=saveLog,saveLogininfor, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311103551}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.resource.api.RemoteFileService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.resource.api.RemoteFileService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=selectByIds,selectUrlByIds,upload,uploadBase64Image, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311103255}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.resource.api.RemoteMessageService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.resource.api.RemoteMessageService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=publishAll,publishMessage, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311104159}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteRoleService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteRoleService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=selectRoleList, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102509}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteRoleService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=selectRoleList, parameterTypes=[org.dromara.system.api.domain.bo.RemoteRoleBo], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteRoleVo>]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteUserService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.system.api.RemoteUserService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=checkEmailUnique,checkPhoneUnique,checkUserAllowed,checkUserDataScope,checkUserNameUnique,deleteUserByIds,faceRecognition,getUserInfo,getUserInfoByEmail,getUserInfoByOpenid,getUserInfoByPhoneNumber,insertAppUser,insertUser,recordLoginInfo,registerUserInfo,selectEmailById,selectListByIds,selectNicknameById,selectNicknameByIds,selectPageUserList,selectPhoneNumberById,selectUserById,selectUserIdsByRoleIds,selectUserNameById,updateBatchById,updateUserInfo,updateUserStatus,userAssessorCount, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311103468}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteDataScopeService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteDataScopeService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=getDeptAndChild,getRoleCustom, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102547}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteDataScopeService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=getDeptAndChild, parameterTypes=[java.lang.Long], returnType=java.lang.String], MethodDefinition [name=getRoleCustom, parameterTypes=[java.lang.Long], returnType=java.lang.String]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteUserService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteUserService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=checkEmailUnique,checkPhoneUnique,checkUserAllowed,checkUserDataScope,checkUserNameUnique,deleteUserByIds,faceRecognition,getUserInfo,getUserInfoByEmail,getUserInfoByOpenid,getUserInfoByPhoneNumber,insertAppUser,insertUser,recordLoginInfo,registerUserInfo,selectEmailById,selectListByIds,selectNicknameById,selectNicknameByIds,selectPageUserList,selectPhoneNumberById,selectUserById,selectUserIdsByRoleIds,selectUserNameById,updateBatchById,updateUserInfo,updateUserStatus,userAssessorCount, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102315}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteUserService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=getUserInfo, parameterTypes=[java.lang.String, java.lang.String], returnType=org.dromara.system.api.model.LoginUser], MethodDefinition [name=getUserInfo, parameterTypes=[java.lang.Long, java.lang.String], returnType=org.dromara.system.api.model.LoginUser], MethodDefinition [name=insertUser, parameterTypes=[org.dromara.system.api.domain.bo.RemoteUserBo], returnType=int], MethodDefinition [name=checkUserNameUnique, parameterTypes=[org.dromara.system.api.domain.bo.RemoteUserBo], returnType=boolean], MethodDefinition [name=selectUserById, parameterTypes=[java.lang.Long], returnType=org.dromara.system.api.domain.vo.RemoteUserVo], MethodDefinition [name=checkPhoneUnique, parameterTypes=[org.dromara.system.api.domain.bo.RemoteUserBo], returnType=boolean], MethodDefinition [name=selectPageUserList, parameterTypes=[org.dromara.system.api.domain.bo.RemoteUserBo], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteUserVo>], MethodDefinition [name=checkEmailUnique, parameterTypes=[org.dromara.system.api.domain.bo.RemoteUserBo], returnType=boolean], MethodDefinition [name=updateUserStatus, parameterTypes=[java.lang.Long, java.lang.String], returnType=int], MethodDefinition [name=deleteUserByIds, parameterTypes=[java.lang.Long[]], returnType=int], MethodDefinition [name=checkUserAllowed, parameterTypes=[java.lang.Long], returnType=void], MethodDefinition [name=checkUserDataScope, parameterTypes=[java.lang.Long], returnType=void], MethodDefinition [name=updateUserInfo, parameterTypes=[org.dromara.system.api.domain.bo.RemoteUserBo], returnType=int], MethodDefinition [name=recordLoginInfo, parameterTypes=[java.lang.Long, java.lang.String], returnType=void], MethodDefinition [name=registerUserInfo, parameterTypes=[org.dromara.system.api.domain.bo.RemoteUserBo], returnType=java.lang.Boolean], MethodDefinition [name=selectUserNameById, parameterTypes=[java.lang.Long], returnType=java.lang.String], MethodDefinition [name=insertAppUser, parameterTypes=[org.dromara.system.api.domain.bo.RemoteUserBo], returnType=org.dromara.system.api.domain.bo.RemoteUserBo], MethodDefinition [name=selectListByIds, parameterTypes=[java.util.List<java.lang.Long>], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteUserVo>], MethodDefinition [name=selectPhoneNumberById, parameterTypes=[java.lang.Long], returnType=java.lang.String], MethodDefinition [name=getUserInfoByPhoneNumber, parameterTypes=[java.lang.String, java.lang.String, boolean], returnType=org.dromara.system.api.model.LoginUser], MethodDefinition [name=getUserInfoByPhoneNumber, parameterTypes=[java.lang.String, java.lang.String], returnType=org.dromara.system.api.model.LoginUser], MethodDefinition [name=selectNicknameByIds, parameterTypes=[java.lang.String], returnType=java.lang.String], MethodDefinition [name=selectUserIdsByRoleIds, parameterTypes=[java.util.List<java.lang.Long>], returnType=java.util.List<java.lang.Long>], MethodDefinition [name=getUserInfoByOpenid, parameterTypes=[java.lang.String], returnType=org.dromara.system.api.model.XcxLoginUser], MethodDefinition [name=faceRecognition, parameterTypes=[java.lang.String, java.lang.String], returnType=org.dromara.common.core.domain.R], MethodDefinition [name=selectNicknameById, parameterTypes=[java.lang.Long], returnType=java.lang.String], MethodDefinition [name=userAssessorCount, parameterTypes=[], returnType=java.lang.Integer], MethodDefinition [name=selectEmailById, parameterTypes=[java.lang.Long], returnType=java.lang.String], MethodDefinition [name=updateBatchById, parameterTypes=[java.util.List<org.dromara.system.api.domain.bo.RemoteUserBo>], returnType=void], MethodDefinition [name=getUserInfoByEmail, parameterTypes=[java.lang.String, java.lang.String], returnType=org.dromara.system.api.model.LoginUser]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteSocialService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteSocialService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=deleteWithValidById,insertByBo,queryList,selectByAuthId,updateByBo, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102392}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteSocialService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=queryList, parameterTypes=[org.dromara.system.api.domain.bo.RemoteSocialBo], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteSocialVo>], MethodDefinition [name=insertByBo, parameterTypes=[org.dromara.system.api.domain.bo.RemoteSocialBo], returnType=void], MethodDefinition [name=updateByBo, parameterTypes=[org.dromara.system.api.domain.bo.RemoteSocialBo], returnType=void], MethodDefinition [name=deleteWithValidById, parameterTypes=[java.lang.Long], returnType=java.lang.Boolean], MethodDefinition [name=selectByAuthId, parameterTypes=[java.lang.String], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteSocialVo>]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteLogService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteLogService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=saveLog,saveLogininfor, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102529}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteLogService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=saveLog, parameterTypes=[org.dromara.system.api.domain.bo.RemoteOperLogBo], returnType=void], MethodDefinition [name=saveLogininfor, parameterTypes=[org.dromara.system.api.domain.bo.RemoteLogininforBo], returnType=void]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteDictService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.system.api.RemoteDictService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=selectDictDataByType, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311104398}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteDeptService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteDeptService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=checkDeptDataScope,selectBatchIds,selectById,selectDeptNameByIds, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102563}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteDeptService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=selectById, parameterTypes=[java.lang.String], returnType=org.dromara.system.api.domain.vo.RemoteDeptVo], MethodDefinition [name=checkDeptDataScope, parameterTypes=[java.lang.Long], returnType=void], MethodDefinition [name=selectBatchIds, parameterTypes=[java.util.List<java.lang.Long>], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteDeptVo>], MethodDefinition [name=selectDeptNameByIds, parameterTypes=[java.lang.String], returnType=java.lang.String]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteTenantService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteTenantService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=queryByTenantId,queryList, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102597}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteTenantService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=queryList, parameterTypes=[], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteTenantVo>], MethodDefinition [name=queryByTenantId, parameterTypes=[java.lang.String], returnType=org.dromara.system.api.domain.vo.RemoteTenantVo]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteLogService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.system.api.RemoteLogService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=saveLog,saveLogininfor, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311104739}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.aea.api.RemoteElderService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.aea.api.RemoteElderService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=getElder,getElderByNameAndIdCard,getElderList,getElderTaskInfoById,selectElderNameById,selectList,selectListByIds, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311103939}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteDataScopeService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.system.api.RemoteDataScopeService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=getDeptAndChild,getRoleCustom, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311103766}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteClientService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.system.api.RemoteClientService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=queryByAppClientId,queryByClientId, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311104549}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteAppLogService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteAppLogService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=saveLog,saveLogininfor, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102631}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteAppLogService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=saveLog, parameterTypes=[org.dromara.system.api.domain.bo.RemoteAppOperLogBo], returnType=void], MethodDefinition [name=saveLogininfor, parameterTypes=[org.dromara.system.api.domain.bo.RemoteAppLogininforBo], returnType=void]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteConfigService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteConfigService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=selectAppRegisterEnabled,selectRegisterEnabled, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102614}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteConfigService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=selectAppRegisterEnabled, parameterTypes=[java.lang.String], returnType=boolean], MethodDefinition [name=selectRegisterEnabled, parameterTypes=[java.lang.String], returnType=boolean]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteAppUserService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteAppUserService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=ImUserInfoByPhoneNumber,getAppUserInfo,getAppUserInfoByEmail,getAppUserInfoByOpenid,getAppUserInfoByOrgIdAndSystemType,getAppUserInfoByPhoneNumber,insertAppUser,recordLoginInfo,registerUserInfo,selectAppUserByIds,selectChatIdentityById,selectList,selectListByIds,selectListByTaskId,selectNickNameById,selectPhoneNumberById,selectUserNameById,selectVoByAppUserId,selectVoList,selectVoListByIds,updateAppUserIdentity,updateTaskId,updateUserInfo, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311101527}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteAppUserService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=selectList, parameterTypes=[java.lang.String], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteAppUserVo>], MethodDefinition [name=updateUserInfo, parameterTypes=[org.dromara.system.api.domain.bo.RemoteAppUserBo], returnType=int], MethodDefinition [name=recordLoginInfo, parameterTypes=[java.lang.Long, java.lang.String], returnType=void], MethodDefinition [name=registerUserInfo, parameterTypes=[org.dromara.system.api.domain.bo.RemoteAppUserBo], returnType=java.lang.Boolean], MethodDefinition [name=selectUserNameById, parameterTypes=[java.lang.Long], returnType=java.lang.String], MethodDefinition [name=insertAppUser, parameterTypes=[org.dromara.system.api.domain.bo.RemoteAppUserBo], returnType=org.dromara.system.api.domain.bo.RemoteAppUserBo], MethodDefinition [name=selectNickNameById, parameterTypes=[java.lang.Long], returnType=java.lang.String], MethodDefinition [name=selectVoListByIds, parameterTypes=[java.util.List<java.lang.Long>], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteAppUserVo>], MethodDefinition [name=selectListByTaskId, parameterTypes=[java.lang.Long], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteAppUserVo>], MethodDefinition [name=getAppUserInfo, parameterTypes=[java.lang.String, java.lang.String, java.lang.String], returnType=org.dromara.system.api.model.AppLoginUser], MethodDefinition [name=getAppUserInfo, parameterTypes=[java.lang.Long, java.lang.String, java.lang.String], returnType=org.dromara.system.api.model.AppLoginUser], MethodDefinition [name=selectListByIds, parameterTypes=[java.util.List<java.lang.Long>], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteAppUserVo>], MethodDefinition [name=selectAppUserByIds, parameterTypes=[java.util.List<java.lang.String>], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteAppUserVo>], MethodDefinition [name=updateTaskId, parameterTypes=[org.dromara.system.api.domain.vo.RemoteAppUserVo], returnType=java.lang.Boolean], MethodDefinition [name=selectVoList, parameterTypes=[org.dromara.system.api.domain.bo.RemoteAppUserBo], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteAppUserVo>], MethodDefinition [name=selectPhoneNumberById, parameterTypes=[java.lang.Long], returnType=java.lang.String], MethodDefinition [name=selectChatIdentityById, parameterTypes=[java.lang.Long], returnType=java.lang.String], MethodDefinition [name=updateAppUserIdentity, parameterTypes=[java.lang.Long, java.lang.String], returnType=java.lang.Boolean], MethodDefinition [name=getAppUserInfoByEmail, parameterTypes=[java.lang.String, java.lang.String], returnType=org.dromara.system.api.model.AppLoginUser], MethodDefinition [name=selectVoByAppUserId, parameterTypes=[java.lang.Long], returnType=org.dromara.system.api.domain.vo.RemoteAppUserVo], MethodDefinition [name=ImUserInfoByPhoneNumber, parameterTypes=[java.lang.String, java.lang.String, java.lang.String], returnType=org.dromara.system.api.model.AppLoginUser], MethodDefinition [name=getAppUserInfoByOpenid, parameterTypes=[java.lang.String], returnType=org.dromara.system.api.model.XcxLoginUser], MethodDefinition [name=getAppUserInfoByPhoneNumber, parameterTypes=[java.lang.String, java.lang.String, java.lang.String], returnType=org.dromara.system.api.model.AppLoginUser], MethodDefinition [name=getAppUserInfoByOrgIdAndSystemType, parameterTypes=[java.lang.Long, java.lang.String], returnType=java.util.List<org.dromara.system.api.model.AppLoginUser>]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteAppUserDetailService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteAppUserDetailService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=selectById, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102472}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteAppUserDetailService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=selectById, parameterTypes=[org.dromara.system.api.domain.bo.RemoteAppUserDetailBo], returnType=org.dromara.system.api.domain.vo.RemoteAppUserDetailVo]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteDeptService', version='', group='', side='consumer'}; definition: {metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, interface=org.dromara.system.api.RemoteDeptService, application=cyly-system, side=consumer, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=checkDeptDataScope,selectBatchIds,selectById,selectDeptNameByIds, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311102654}, dubbo version: 3.3.4, current host: ***********
2025-06-08 05:57:19 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-system', serviceInterface='org.dromara.system.api.RemoteAddressService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={metadata-type=remote, dubbo=2.0.2, pid=13188, release=3.3.4, anyhost=true, interface=org.dromara.system.api.RemoteAddressService, application=cyly-system, side=provider, executor-management-mode=isolation, file-cache=true, methods=checkCityExists,checkCommunityExists,checkDistrictExists,checkProvinceExists,checkStreetExists,selectBatchIds,selectByFullNameList,validateRegionNames, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20881, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311102365}} ServiceDefinition [canonicalName=org.dromara.system.api.RemoteAddressService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-system/target/classes/, methods=[MethodDefinition [name=checkCityExists, parameterTypes=[java.lang.String, java.lang.String], returnType=boolean], MethodDefinition [name=checkStreetExists, parameterTypes=[java.lang.String, java.lang.String, java.lang.String, java.lang.String], returnType=boolean], MethodDefinition [name=selectBatchIds, parameterTypes=[java.util.List<java.lang.Long>], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteDictAddressVo>], MethodDefinition [name=checkProvinceExists, parameterTypes=[java.lang.String], returnType=boolean], MethodDefinition [name=validateRegionNames, parameterTypes=[java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String], returnType=org.dromara.common.core.domain.R], MethodDefinition [name=selectByFullNameList, parameterTypes=[java.util.List<java.lang.String>], returnType=java.util.List<org.dromara.system.api.domain.vo.RemoteDictAddressVo>], MethodDefinition [name=checkDistrictExists, parameterTypes=[java.lang.String, java.lang.String, java.lang.String], returnType=boolean], MethodDefinition [name=checkCommunityExists, parameterTypes=[java.lang.String, java.lang.String, java.lang.String, java.lang.String, java.lang.String], returnType=boolean]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:23:06 [NettyServerWorker-5-6] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0xb6e1eb0a, L:/***********:20881 ! R:/***********:51619] of ***********:51619 -> ***********:20881 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:23:45 [NettyServerWorker-5-7] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x9a82e3b7, L:/***********:20881 - R:/***********:51978] of ***********:20881 -> ***********:51978 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:30:45 [NettyServerWorker-5-7] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] IdleStateEvent triggered, close channel NettyChannel [channel=[id: 0x9a82e3b7, L:/***********:20881 - R:/***********:51978]], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:30:45 [NettyServerWorker-5-7] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x9a82e3b7, L:/***********:20881 - R:/***********:51978], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:30:45 [NettyServerWorker-5-7] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x9a82e3b7, L:/***********:20881 ! R:/***********:51978] of ***********:51978 -> ***********:20881 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:33:26 [NettyServerWorker-5-8] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x4e37c148, L:/***********:20881 - R:/***********:52883] of ***********:20881 -> ***********:52883 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:33:29 [NettyServerWorker-5-8] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x4e37c148, L:/***********:20881 ! R:/***********:52883] of ***********:52883 -> ***********:20881 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:34:00 [NettyServerWorker-5-9] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x71b1cc10, L:/***********:20881 - R:/***********:53007] of ***********:20881 -> ***********:53007 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:35:52 [NettyServerWorker-5-9] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x71b1cc10, L:/***********:20881 ! R:/***********:53007] of ***********:53007 -> ***********:20881 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:36:18 [NettyServerWorker-5-10] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x7cdb2db0, L:/***********:20881 - R:/***********:53318] of ***********:20881 -> ***********:53318 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:44:34 [NettyServerWorker-5-10] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] IdleStateEvent triggered, close channel NettyChannel [channel=[id: 0x7cdb2db0, L:/***********:20881 - R:/***********:53318]], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:44:34 [NettyServerWorker-5-10] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x7cdb2db0, L:/***********:20881 - R:/***********:53318], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:44:34 [NettyServerWorker-5-10] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x7cdb2db0, L:/***********:20881 ! R:/***********:53318] of ***********:53318 -> ***********:20881 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:51:27 [NettyServerWorker-5-11] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x76c2466f, L:/***********:20881 - R:/***********:54841] of ***********:20881 -> ***********:54841 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:51:29 [NettyServerWorker-5-11] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x76c2466f, L:/***********:20881 ! R:/***********:54841] of ***********:54841 -> ***********:20881 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:07 [NettyServerWorker-5-12] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0xb5fab8c8, L:/***********:20881 - R:/***********:54995] of ***********:20881 -> ***********:54995 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:26 [NettyServerWorker-5-2] INFO  o.a.d.rpc.protocol.dubbo.DubboCodec -  [DUBBO] Because thread pool isolation is enabled on the dubbo protocol, the body can only be decoded on the io thread, and the parameter[decode.in.io.thread] will be ignored, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:27 [DubboServerHandler-***********:20881-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByAppClientId]
2025-06-08 08:52:28 [DubboServerHandler-***********:20881-thread-20] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByAppClientId],SpendTime=[221ms]
2025-06-08 08:52:28 [DubboServerHandler-***********:20881-thread-21] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[getAppUserInfo]
2025-06-08 08:52:28 [DubboServerHandler-***********:20881-thread-21] INFO  o.d.s.dubbo.RemoteAppUserServiceImpl - 用户登录成功，用户信息：AppUserVo(userId=1929882877257396226, deptId=1891659126946738178, roleId=14, systemType=aea, tenantId=000000, userName=wtc, nickName=wtc, userType=app_user, email=, phoneNumber=18876764511, sex=0, FaceUrl=null, avatar=null, password=3da53c14263fe6c93a9210f4f7fa35860337b1fac795ecd81df4611d658515ed, salt=d07fa3a06256c197ebf4c2e192d93a60, status=0, loginIp=127.0.0.1, loginDate=Fri Jun 06 18:29:11 CST 2025, remark=null, createTime=Tue Jun 03 20:48:28 CST 2025, taskId=null, chatIdentity=null)
2025-06-08 08:52:28 [DubboServerHandler-***********:20881-thread-21] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[getAppUserInfo],SpendTime=[423ms]
2025-06-08 08:52:28 [DubboServerHandler-***********:20881-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned]
2025-06-08 08:52:28 [DubboServerHandler-***********:20881-thread-22] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned],SpendTime=[10ms]
2025-06-08 08:52:29 [DubboServerHandler-***********:20881-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo]
2025-06-08 08:52:29 [DubboServerHandler-***********:20881-thread-24] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor]
2025-06-08 08:52:29 [DubboServerHandler-***********:20881-thread-24] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor],SpendTime=[18ms]
2025-06-08 08:52:29 [DubboServerHandler-***********:20881-thread-23] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo],SpendTime=[59ms]
2025-06-08 08:52:29 [DubboServerHandler-***********:20881-thread-25] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo]
2025-06-08 08:52:29 [DubboServerHandler-***********:20881-thread-26] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor]
2025-06-08 08:52:29 [DubboServerHandler-***********:20881-thread-26] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-08 08:52:29 [DubboServerHandler-***********:20881-thread-25] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo],SpendTime=[111ms]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-27] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-27] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[5ms]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-28] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-28] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[49ms]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-29] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-29] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned],SpendTime=[6ms]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-31] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-31] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[0ms]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-30] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-30] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[11ms]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-32] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-08 08:53:27 [DubboServerHandler-***********:20881-thread-32] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-33] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByAppClientId]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-33] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteClientService],MethodName=[queryByAppClientId],SpendTime=[23ms]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-34] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[getAppUserInfo]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-34] INFO  o.d.s.dubbo.RemoteAppUserServiceImpl - 用户登录成功，用户信息：AppUserVo(userId=1929882877257396226, deptId=1891659126946738178, roleId=14, systemType=aea, tenantId=000000, userName=wtc, nickName=wtc, userType=app_user, email=, phoneNumber=18876764511, sex=0, FaceUrl=null, avatar=null, password=3da53c14263fe6c93a9210f4f7fa35860337b1fac795ecd81df4611d658515ed, salt=d07fa3a06256c197ebf4c2e192d93a60, status=0, loginIp=************, loginDate=Sun Jun 08 08:52:29 CST 2025, remark=null, createTime=Tue Jun 03 20:48:28 CST 2025, taskId=null, chatIdentity=null)
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-34] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[getAppUserInfo],SpendTime=[36ms]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-35] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-35] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned],SpendTime=[10ms]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-36] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-37] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-37] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor],SpendTime=[2ms]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-36] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo],SpendTime=[117ms]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-38] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-39] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-39] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor],SpendTime=[0ms]
2025-06-08 09:36:14 [DubboServerHandler-***********:20881-thread-38] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo],SpendTime=[35ms]
