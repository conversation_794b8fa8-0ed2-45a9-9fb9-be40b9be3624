2025-06-08 08:51:46 [reactor-http-nio-9] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 08:53:17 [reactor-http-nio-5] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 09:00:29 [reactor-http-nio-7] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 09:00:33 [reactor-http-nio-7] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 09:01:15 [reactor-http-nio-3] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 09:27:21 [reactor-http-nio-4] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 09:27:21 [reactor-http-nio-6] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 10:26:06 [reactor-http-nio-3] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 10:26:07 [reactor-http-nio-3] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 10:26:07 [reactor-http-nio-3] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 10:26:07 [reactor-http-nio-3] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/favicon.ico,异常信息:404 NOT_FOUND "No static resource favicon.ico."
2025-06-08 15:25:26 [loomBoundedElastic-52700] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/aea/taskBase/commit,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for cyly-aea"
2025-06-08 15:27:04 [loomBoundedElastic-52786] ERROR o.d.c.g.h.GatewayExceptionHandler - [网关异常处理]请求路径:/aea/taskBase/commit,异常信息:503 SERVICE_UNAVAILABLE "Unable to find instance for cyly-aea"
2025-06-08 16:38:15 [Thread-34] ERROR o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] Failed to subscribe DUBBO_GROUP:mapping:queues, cause: java.net.SocketException: Connection reset, dubbo version: 3.3.4, current host: ***********, error code: 6-14. This may be caused by , go to https://dubbo.apache.org/faq/6/14 to find instructions. 
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:251)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:47)
	at redis.clients.jedis.Protocol.process(Protocol.java:135)
	at redis.clients.jedis.Protocol.read(Protocol.java:221)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:350)
	at redis.clients.jedis.Connection.getUnflushedObject(Connection.java:316)
	at redis.clients.jedis.JedisPubSubBase.process(JedisPubSubBase.java:115)
	at redis.clients.jedis.JedisPubSubBase.proceed(JedisPubSubBase.java:92)
	at redis.clients.jedis.Jedis.subscribe(Jedis.java:7941)
	at org.apache.dubbo.metadata.store.redis.RedisMetadataReport$MappingDataListener.run(RedisMetadataReport.java:497)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/java.io.InputStream.read(InputStream.java:220)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:245)
	... 9 common frames omitted
