2025-06-08 03:28:05 [DubboMetadataReportTimer-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] start to publish all metadata., dubbo version: 3.3.4, current host: ***********
2025-06-08 03:28:05 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-gateway', serviceInterface='org.dromara.system.api.RemoteIpBanService', version='', group='', side='consumer'}; definition: {dubbo=2.0.2, side=consumer, pid=151820, application=cyly-gateway, metadata-type=remote, interface=org.dromara.system.api.RemoteIpBanService, release=3.3.4, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=banNetwork,isNetworkBanned, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311082207}, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:24:31 [loomBoundedElastic-29156] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:24:33 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],耗时:[1447]毫秒
2025-06-08 08:27:55 [loomBoundedElastic-29325] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:34:08 [loomBoundedElastic-29658] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:35:01 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],耗时:[53313]毫秒
2025-06-08 08:35:16 [loomBoundedElastic-29715] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:35:31 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],耗时:[15100]毫秒
2025-06-08 08:35:36 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:35:37 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],耗时:[83]毫秒
2025-06-08 08:36:48 [loomBoundedElastic-29800] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:36:49 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],耗时:[949]毫秒
2025-06-08 08:39:05 [loomBoundedElastic-29941] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:39:34 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],耗时:[28670]毫秒
2025-06-08 08:39:39 [reactor-http-nio-7] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:41:34 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],耗时:[115645]毫秒
2025-06-08 08:42:33 [loomBoundedElastic-30138] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:52:16 [loomBoundedElastic-30611] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 08:52:20 [loomBoundedElastic-30612] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],参数类型[param],参数:[{"n":["**********"]}]
2025-06-08 08:52:23 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[3427]毫秒
2025-06-08 08:52:26 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/app/login],参数类型[json],参数:[{"userName":"8f10943604c1c02c4405710083457ca7de15062231bdb31387237a58a7724ab6dcab9da94e65da78a752c552c1fe475d1f408bc319a7deff7cfee9f46ee4cdcffbcfa362ac41ba950b3c2130e2edbf2d38bb83d67752415aeaa02a440c497874eb1822","password":"5bbed6814e4f0e58b92b876d53b3cf079d852d621645b7b35b796736147a023f921ecb46c1cdd0ed2b0604a957f762d1e6bd88f68d3c047769150b5920faa435b30624ba36c74202af8f94cff47f42b49b39fba944373ea587c66e20361a5d0e316336d275e6","uuid":"d2273b5d730648389093deea79e4bd1e","code":"1","type":"1","systemType":"aea","grantType":"password","clientId":"428a8310cd442757ae699df5d894f051"}]
2025-06-08 08:52:29 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/app/login],耗时:[3056]毫秒
2025-06-08 08:52:29 [loomBoundedElastic-30641] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/profile/usrapi/info],参数类型[param],参数:[{"n":["**********"]}]
2025-06-08 08:52:31 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],耗时:[14770]毫秒
2025-06-08 08:52:31 [reactor-http-nio-7] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/profile/usrapi/info],耗时:[2027]毫秒
2025-06-08 08:52:44 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["**********"]}]
2025-06-08 08:52:44 [reactor-http-nio-11] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["**********"]}]
2025-06-08 08:52:44 [reactor-http-nio-7] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[43]毫秒
2025-06-08 08:52:44 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[48]毫秒
2025-06-08 08:52:44 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/logout],无参数
2025-06-08 08:52:44 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/logout],耗时:[53]毫秒
2025-06-08 08:52:44 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 08:52:44 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 08:52:45 [loomBoundedElastic-30642] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse/close],无参数
2025-06-08 08:52:45 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[251]毫秒
2025-06-08 08:52:45 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[294]毫秒
2025-06-08 08:52:45 [reactor-http-nio-7] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 08:52:45 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 08:52:45 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[5]毫秒
2025-06-08 08:52:45 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[26]毫秒
2025-06-08 08:52:46 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse/close],耗时:[1507]毫秒
2025-06-08 08:53:22 [loomBoundedElastic-30671] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],无参数
2025-06-08 08:53:22 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[30]毫秒
2025-06-08 08:53:22 [reactor-http-nio-7] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 08:53:22 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-06-08 08:53:27 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/login],参数类型[json],参数:[{"tenantId":"000000","username":"F3MiZdWSffZkMjjI15QYM0DWwHGUnfOdRyI0w2itFSU/awhRFdhr1Ke3oEdgV2YabeSIjDeF5j2eiJdYetDLvgYMft2vK34nceBeC77C7g2h6qsyR9IQU9Tsk5fbbPBASpQCrcc=","password":"nVCbFTKqDZix44EZs1n/kXp+SFjm2WDdGQxTu+KeFfnEP09RTeDff5HrXX3gEb/sujqfwu5btH6Z+rbXlojV78xxMQHEXWsVdUl/KLaYMEC4nQMStTxJKAJlr0i7A/gJwphtqLcwBVh+0bc=","rememberMe":true,"uuid":"5a5958d9350643c58998a91fd289fce4","code":"14","clientId":"e5cd7e4891bf95d1d19206ce24a7b32e","grantType":"password"}]
2025-06-08 08:53:27 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/login],耗时:[241]毫秒
2025-06-08 08:53:28 [reactor-http-nio-11] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/tenant/list],无参数
2025-06-08 08:53:28 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/tenant/list],耗时:[4]毫秒
2025-06-08 08:53:29 [loomBoundedElastic-30701] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/treeList],无参数
2025-06-08 08:53:29 [loomBoundedElastic-30700] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/list],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-08 08:53:29 [loomBoundedElastic-30702] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /resource/sse],参数类型[param],参数:[{"Authorization":["Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJwRFJ6WGRUcDdZTVREMWlGTVl2b2dtTVozWVdMc1M1byIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTA2LCJkZXB0TmFtZSI6IiIsInVzZXJUeXBlIjoic3lzX3VzZXIiLCJuaWNrTmFtZSI6IueWr-eLgueahOeLruWtkExpIn0._F2luKCdwgtRbLSE-6QyyrBmdz48CMFGP6wSTLqa59U"],"clientid":["e5cd7e4891bf95d1d19206ce24a7b32e"]}]
2025-06-08 08:53:29 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/treeList],耗时:[29]毫秒
2025-06-08 08:53:29 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /resource/sse],耗时:[30]毫秒
2025-06-08 08:53:29 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/list],耗时:[144]毫秒
2025-06-08 08:53:30 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/pageTreeList],参数类型[param],参数:[{"pageNum":["1"],"pageSize":["10"]}]
2025-06-08 08:53:30 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/pageTreeList],耗时:[16]毫秒
2025-06-08 08:57:33 [loomBoundedElastic-30927] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["**********"]}]
2025-06-08 08:57:33 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[15]毫秒
2025-06-08 08:57:37 [reactor-http-nio-7] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749344259"]}]
2025-06-08 08:57:37 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[11]毫秒
2025-06-08 08:57:42 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749344259"]}]
2025-06-08 08:57:42 [loomBoundedElastic-30928] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749344259"]}]
2025-06-08 08:57:42 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[12]毫秒
2025-06-08 08:57:42 [reactor-http-nio-10] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[20]毫秒
2025-06-08 08:58:55 [loomBoundedElastic-30985] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749344337"]}]
2025-06-08 08:58:55 [loomBoundedElastic-30986] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749344337"]}]
2025-06-08 08:58:55 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[16]毫秒
2025-06-08 08:58:55 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[18]毫秒
2025-06-08 09:00:05 [loomBoundedElastic-31071] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749344407"]}]
2025-06-08 09:00:05 [loomBoundedElastic-31072] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749344407"]}]
2025-06-08 09:00:05 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[18]毫秒
2025-06-08 09:00:05 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[19]毫秒
2025-06-08 09:00:59 [loomBoundedElastic-31129] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],参数类型[json],参数:[{
    "taskId": 1931322380912914433,
    "elderId": 1929791583771910100,
    "assessorEvaluateUserId": 1910267489927868400,
    "deputyEvaluateUserId": 1910949462379901000,
    "assessorSign": "https://oss.example.com/sign/assessor.png",
    "deputySign": "https://oss.example.com/sign/deputy.png",
    "informationProviderSign": "https://oss.example.com/sign/provider.png",
    "location": "XX社区服务中心",
    "reasonCode": 1,
    "reportUrl": "https://oss.example.com/report/1001.pdf",
    "selfScore": 10,
    "baseScore": 8,
    "mentionScore": 7,
    "feelScore": 7,
    "totalScore": 32,
    "firstLevel": 5,
    "adjustmentBasis": "1,3",
    "finalLevel": 5,
    "answerId": 6001,
    "content": "长者自述身体状况良好，但需注意饮食控制。",
    "score": 80,
    "evaluateUserId": 1910267489927868400
}]
2025-06-08 09:01:17 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/v3/api-docs/default],无参数
2025-06-08 09:02:32 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /aea-refactor/questionnaire-evaluate/submit-answers],耗时:[93057]毫秒
2025-06-08 09:02:36 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/v3/api-docs/default],耗时:[79517]毫秒
2025-06-08 09:10:01 [loomBoundedElastic-31654] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749345003"]}]
2025-06-08 09:10:01 [loomBoundedElastic-31655] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749345003"]}]
2025-06-08 09:10:01 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[83]毫秒
2025-06-08 09:10:01 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[83]毫秒
2025-06-08 09:11:16 [loomBoundedElastic-31712] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749345078"]}]
2025-06-08 09:11:16 [loomBoundedElastic-31713] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749345078"]}]
2025-06-08 09:11:16 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[16]毫秒
2025-06-08 09:11:16 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[19]毫秒
2025-06-08 09:15:10 [loomBoundedElastic-31939] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749345312"]}]
2025-06-08 09:15:10 [loomBoundedElastic-31938] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749345312"]}]
2025-06-08 09:15:10 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[13]毫秒
2025-06-08 09:15:10 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[23]毫秒
2025-06-08 09:15:45 [reactor-http-nio-11] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749345312"]}]
2025-06-08 09:15:45 [reactor-http-nio-10] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749345312"]}]
2025-06-08 09:15:45 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[13]毫秒
2025-06-08 09:15:45 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[19]毫秒
2025-06-08 09:17:24 [loomBoundedElastic-32053] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749345312"]}]
2025-06-08 09:17:24 [loomBoundedElastic-32052] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749345312"]}]
2025-06-08 09:17:24 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[13]毫秒
2025-06-08 09:17:24 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[20]毫秒
2025-06-08 09:20:26 [loomBoundedElastic-32223] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749345628"]}]
2025-06-08 09:20:26 [loomBoundedElastic-32222] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749345628"]}]
2025-06-08 09:20:26 [reactor-http-nio-7] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[49]毫秒
2025-06-08 09:20:26 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[55]毫秒
2025-06-08 09:24:14 [loomBoundedElastic-32448] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["1927985268001239041"],"n":["1749345830"]}]
2025-06-08 09:24:14 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[22]毫秒
2025-06-08 09:24:15 [reactor-http-nio-10] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["1927985268001239041"],"n":["1749345857"]}]
2025-06-08 09:24:15 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[15]毫秒
2025-06-08 09:24:25 [reactor-http-nio-11] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["1927985268001239041"],"n":["1749345867"]}]
2025-06-08 09:24:26 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[16]毫秒
2025-06-08 09:26:26 [loomBoundedElastic-32561] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["1927985268001239041"],"n":["1749345966"]}]
2025-06-08 09:26:26 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[75]毫秒
2025-06-08 09:27:04 [loomBoundedElastic-32618] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1927985310195937281"],"n":["1749346023"]}]
2025-06-08 09:27:04 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[59]毫秒
2025-06-08 09:27:06 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1928001343241744386"],"n":["1749346023"]}]
2025-06-08 09:27:07 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[21]毫秒
2025-06-08 09:27:21 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/v3/api-docs/default],无参数
2025-06-08 09:27:21 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/v3/api-docs/default],耗时:[17]毫秒
2025-06-08 09:28:06 [loomBoundedElastic-32675] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930104438665076738"],"n":["1749346078"]}]
2025-06-08 09:28:06 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[31]毫秒
2025-06-08 09:28:09 [reactor-http-nio-10] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930099855561678849"],"n":["1749346078"]}]
2025-06-08 09:28:09 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[22]毫秒
2025-06-08 09:28:22 [reactor-http-nio-11] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930104438665076738"],"n":["1749346078"]}]
2025-06-08 09:28:22 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[67]毫秒
2025-06-08 09:28:32 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100095031271426"],"n":["1749346078"]}]
2025-06-08 09:28:32 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[29]毫秒
2025-06-08 09:28:38 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100364691464193"],"n":["1749346078"]}]
2025-06-08 09:28:38 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[57]毫秒
2025-06-08 09:31:51 [loomBoundedElastic-32872] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100095031271426"],"n":["1749346311"]}]
2025-06-08 09:31:51 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[34]毫秒
2025-06-08 09:32:22 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100095031"]}]
2025-06-08 09:32:22 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[34]毫秒
2025-06-08 09:32:35 [loomBoundedElastic-32929] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100095031"]}]
2025-06-08 09:32:35 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[16]毫秒
2025-06-08 09:32:53 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749346371"]}]
2025-06-08 09:32:53 [loomBoundedElastic-32930] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749346371"]}]
2025-06-08 09:32:53 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[25]毫秒
2025-06-08 09:32:53 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[233]毫秒
2025-06-08 09:33:10 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"questionnaireId":["1930100095031"],"parentId":["0"],"n":["1749346371"]}]
2025-06-08 09:33:10 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[16]毫秒
2025-06-08 09:33:19 [loomBoundedElastic-32959] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"questionnaireId":["1930100095031"],"n":["1749346371"]}]
2025-06-08 09:33:19 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[46]毫秒
2025-06-08 09:33:29 [reactor-http-nio-11] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"questionnaireId":["1930100095031"],"n":["1749346371"],"parentId":["0"]}]
2025-06-08 09:33:29 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[122]毫秒
2025-06-08 09:33:36 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930099855561678849"],"n":["1749346371"]}]
2025-06-08 09:33:36 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[26]毫秒
2025-06-08 09:33:54 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100095031","1930099855561678849"],"n":["1749346371"],"parentId":["0"]}]
2025-06-08 09:33:54 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[24]毫秒
2025-06-08 09:34:03 [loomBoundedElastic-33016] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100095031271426"],"n":["1749346371"]}]
2025-06-08 09:34:03 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[29]毫秒
2025-06-08 09:34:20 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100095031"]}]
2025-06-08 09:34:20 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[18]毫秒
2025-06-08 09:34:26 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100095031271426"]}]
2025-06-08 09:34:26 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[22]毫秒
2025-06-08 09:34:29 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930099855561678849"],"n":["1749346371"]}]
2025-06-08 09:34:29 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[23]毫秒
2025-06-08 09:34:35 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930099855561678849"]}]
2025-06-08 09:34:35 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[48]毫秒
2025-06-08 09:34:49 [loomBoundedElastic-33045] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749346371"]}]
2025-06-08 09:34:49 [loomBoundedElastic-33046] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749346371"]}]
2025-06-08 09:34:49 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[24]毫秒
2025-06-08 09:34:49 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[35]毫秒
2025-06-08 09:34:50 [reactor-http-nio-11] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749346371"]}]
2025-06-08 09:34:50 [reactor-http-nio-10] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749346371"]}]
2025-06-08 09:34:50 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[30]毫秒
2025-06-08 09:34:50 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[126]毫秒
2025-06-08 09:34:55 [loomBoundedElastic-33047] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],参数类型[param],参数:[{"n":["1749346371"]}]
2025-06-08 09:34:55 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[489]毫秒
2025-06-08 09:36:03 [loomBoundedElastic-33132] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /auth/code],参数类型[param],参数:[{"n":["1749346565"]}]
2025-06-08 09:36:03 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /auth/code],耗时:[39]毫秒
2025-06-08 09:36:14 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[POST /auth/app/login],参数类型[json],参数:[{"userName":"092d9abcf2610c0484b0ffd44702d121769652e2c71baa167712900a87e43c912c09ef19e5a4edbadb18b6964f54f38522358fc79425b1f2a3ddafd8eb802d850bbb3943757e7cbc3c2806c961b992a5381aee5d633f47a38776efe1459d2fa648cfbe","password":"7b7eaedfc4cf6f8b66662dea7ce0d3491c6867ff043160079f6189bc26a2969b8ca399c8ed142b16b5d0922d24b0d4c6e49926e29d78911347d1ace24dd77267ad58217b9d19a1fb168430af26834571f5f6d7d6e449cbfd15a7ddee27f4ab5b7590c7ed75f2","uuid":"edea39a14f1842cfbf41f103e7a93507","code":"10","type":"1","systemType":"aea","grantType":"password","clientId":"428a8310cd442757ae699df5d894f051"}]
2025-06-08 09:36:14 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[POST /auth/app/login],耗时:[674]毫秒
2025-06-08 09:36:14 [loomBoundedElastic-33133] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/profile/usrapi/info],参数类型[param],参数:[{"n":["1749346565"]}]
2025-06-08 09:36:14 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/profile/usrapi/info],耗时:[39]毫秒
2025-06-08 09:36:16 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749346565"]}]
2025-06-08 09:36:16 [loomBoundedElastic-33134] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749346565"]}]
2025-06-08 09:36:16 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[10]毫秒
2025-06-08 09:36:17 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[19]毫秒
2025-06-08 09:38:21 [loomBoundedElastic-33247] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749346703"]}]
2025-06-08 09:38:21 [loomBoundedElastic-33248] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749346703"]}]
2025-06-08 09:38:21 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[36]毫秒
2025-06-08 09:38:21 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[39]毫秒
2025-06-08 09:39:47 [loomBoundedElastic-33334] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749346789"]}]
2025-06-08 09:39:47 [loomBoundedElastic-33333] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749346789"]}]
2025-06-08 09:39:47 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[16]毫秒
2025-06-08 09:39:47 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[26]毫秒
2025-06-08 09:41:38 [loomBoundedElastic-33448] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749346900"]}]
2025-06-08 09:41:38 [loomBoundedElastic-33447] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749346900"]}]
2025-06-08 09:41:38 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[13]毫秒
2025-06-08 09:41:38 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[16]毫秒
2025-06-08 09:43:46 [loomBoundedElastic-33561] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749347028"]}]
2025-06-08 09:43:46 [loomBoundedElastic-33562] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749347028"]}]
2025-06-08 09:43:46 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[12]毫秒
2025-06-08 09:43:46 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[16]毫秒
2025-06-08 09:44:21 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749347028"]}]
2025-06-08 09:44:21 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749347028"]}]
2025-06-08 09:44:21 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[41]毫秒
2025-06-08 09:44:21 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[44]毫秒
2025-06-08 09:44:22 [loomBoundedElastic-33591] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749347064"]}]
2025-06-08 09:44:22 [loomBoundedElastic-33592] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749347064"]}]
2025-06-08 09:44:22 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[28]毫秒
2025-06-08 09:44:22 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[31]毫秒
2025-06-08 09:44:27 [reactor-http-nio-7] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749347064"]}]
2025-06-08 09:44:27 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749347064"]}]
2025-06-08 09:44:27 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[13]毫秒
2025-06-08 09:44:27 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[14]毫秒
2025-06-08 09:44:39 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930104438665076738"],"n":["1749347064"]}]
2025-06-08 09:44:39 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[32]毫秒
2025-06-08 09:45:23 [loomBoundedElastic-33649] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100265798164482"],"n":["1749347064"]}]
2025-06-08 09:45:23 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[28]毫秒
2025-06-08 09:45:25 [reactor-http-nio-10] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100364691464193"],"n":["1749347064"]}]
2025-06-08 09:45:26 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[28]毫秒
2025-06-08 09:47:00 [loomBoundedElastic-33762] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100364691464193"],"n":["1749347222"]}]
2025-06-08 09:47:01 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[30]毫秒
2025-06-08 09:47:09 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100364691464193"],"n":["1749347231"]}]
2025-06-08 09:47:09 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[24]毫秒
2025-06-08 09:47:32 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930104438665076738"],"n":["1749347231"]}]
2025-06-08 09:47:32 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[34]毫秒
2025-06-08 09:47:42 [loomBoundedElastic-33791] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100364691464193"],"n":["1749347231"]}]
2025-06-08 09:47:42 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[24]毫秒
2025-06-08 09:47:44 [reactor-http-nio-4] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930104438665076738"],"n":["1749347231"]}]
2025-06-08 09:47:44 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[20]毫秒
2025-06-08 09:47:49 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930104438665076738"],"n":["1749347231"]}]
2025-06-08 09:47:49 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[33]毫秒
2025-06-08 09:47:56 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100265798164482"],"n":["1749347231"]}]
2025-06-08 09:47:57 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[24]毫秒
2025-06-08 09:48:12 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930104438665076738"],"n":["1749347231"]}]
2025-06-08 09:48:12 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[26]毫秒
2025-06-08 09:48:33 [loomBoundedElastic-33848] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100265798164482"],"n":["1749347304"]}]
2025-06-08 09:48:33 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[27]毫秒
2025-06-08 09:48:59 [reactor-http-nio-10] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749347304"]}]
2025-06-08 09:48:59 [loomBoundedElastic-33877] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749347304"]}]
2025-06-08 09:48:59 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[15]毫秒
2025-06-08 09:48:59 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[18]毫秒
2025-06-08 09:49:16 [loomBoundedElastic-33878] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930100095031271426"],"n":["1749347304"]}]
2025-06-08 09:49:16 [reactor-http-nio-12] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[25]毫秒
2025-06-08 09:50:46 [loomBoundedElastic-33963] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930099855561678849"],"n":["1749347446"]}]
2025-06-08 09:50:46 [reactor-http-nio-3] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[29]毫秒
2025-06-08 09:51:54 [loomBoundedElastic-34020] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],参数类型[param],参数:[{"questionnaireId":["1930099855561678849"],"n":["1749347498"]}]
2025-06-08 09:51:54 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaireQuestion/usrapi/list],耗时:[25]毫秒
2025-06-08 09:51:57 [reactor-http-nio-6] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749347498"]}]
2025-06-08 09:51:57 [loomBoundedElastic-34021] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749347498"]}]
2025-06-08 09:51:57 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[15]毫秒
2025-06-08 09:51:57 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[22]毫秒
2025-06-08 09:52:04 [reactor-http-nio-9] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749347526"]}]
2025-06-08 09:52:04 [reactor-http-nio-10] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749347526"]}]
2025-06-08 09:52:04 [reactor-http-nio-5] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[16]毫秒
2025-06-08 09:52:04 [reactor-http-nio-8] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[39]毫秒
2025-06-08 09:53:14 [loomBoundedElastic-34106] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["0"],"n":["1749347596"]}]
2025-06-08 09:53:14 [loomBoundedElastic-34107] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /system/dept/usrapi/deptList],参数类型[param],参数:[{"n":["1749347596"]}]
2025-06-08 09:53:14 [reactor-http-nio-1] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /system/dept/usrapi/deptList],耗时:[13]毫秒
2025-06-08 09:53:14 [reactor-http-nio-2] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[18]毫秒
2025-06-08 15:26:13 [loomBoundedElastic-52757] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]开始请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],参数类型[param],参数:[{"parentId":["1927985268001239041"],"n":["1749367574"]}]
2025-06-08 15:26:13 [reactor-http-nio-7] INFO  o.d.c.gateway.filter.GlobalLogFilter - [PLUS]结束请求 => URL[GET /aea-refactor/questionnaire/usrapi/treeList],耗时:[152]毫秒
