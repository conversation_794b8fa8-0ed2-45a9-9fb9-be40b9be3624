2025-06-08 03:07:37 [DubboMetadataReportTimer-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] start to publish all metadata., dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.xchat.api.model.RemoteChatUserImService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.xchat.api.model.RemoteChatUserImService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=insertChatUserIm,selectAllSysUserImList,selectUserByPhoneNumber, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081284}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.system.api.RemoteUserService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.system.api.RemoteUserService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=checkEmailUnique,checkPhoneUnique,checkUserAllowed,checkUserDataScope,checkUserNameUnique,deleteUserByIds,faceRecognition,getUserInfo,getUserInfoByEmail,getUserInfoByOpenid,getUserInfoByPhoneNumber,insertAppUser,insertUser,recordLoginInfo,registerUserInfo,selectEmailById,selectListByIds,selectNicknameById,selectNicknameByIds,selectPageUserList,selectPhoneNumberById,selectUserById,selectUserIdsByRoleIds,selectUserNameById,updateBatchById,updateUserInfo,updateUserStatus,userAssessorCount, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311080288}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.system.api.RemoteAppLogService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.system.api.RemoteAppLogService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=saveLog,saveLogininfor, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311080909}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.system.api.RemoteTenantService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.system.api.RemoteTenantService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=queryByTenantId,queryList, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311080031}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.system.api.RemoteConfigService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.system.api.RemoteConfigService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=selectAppRegisterEnabled,selectRegisterEnabled, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311078490}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.resource.api.RemoteMessageService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.resource.api.RemoteMessageService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=publishAll,publishMessage, logger=slf4j, check=false, qos.enable=false, timeout=3000, register-mode=instance, unloadClusterRelated=false, retries=0, background=false, stub=true, sticky=false, validation=jvalidationNew, timestamp=1749311080689}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.system.api.RemoteIpBanService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.system.api.RemoteIpBanService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=banNetwork,isNetworkBanned, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311080086}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.system.api.RemoteSocialService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.system.api.RemoteSocialService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=deleteWithValidById,insertByBo,queryList,selectByAuthId,updateByBo, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311080487}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.system.api.RemoteAppUserService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.system.api.RemoteAppUserService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=ImUserInfoByPhoneNumber,getAppUserInfo,getAppUserInfoByEmail,getAppUserInfoByOpenid,getAppUserInfoByOrgIdAndSystemType,getAppUserInfoByPhoneNumber,insertAppUser,recordLoginInfo,registerUserInfo,selectAppUserByIds,selectChatIdentityById,selectList,selectListByIds,selectListByTaskId,selectNickNameById,selectPhoneNumberById,selectUserNameById,selectVoByAppUserId,selectVoList,selectVoListByIds,updateAppUserIdentity,updateTaskId,updateUserInfo, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081092}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.system.api.RemoteClientService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.system.api.RemoteClientService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=queryByAppClientId,queryByClientId, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081490}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.xchat.api.model.RemoteNeteaseClient', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.xchat.api.model.RemoteNeteaseClient, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=addFriend,refreshUserToken,register,sendVerificationCode, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081883}, dubbo version: 3.3.4, current host: ***********
2025-06-08 03:07:37 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.system.api.RemoteLogService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.system.api.RemoteLogService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=saveLog,saveLogininfor, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081685}, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:21 [undertow-0] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:************:'
2025-06-08 08:52:23 [undertow-0] INFO  o.d.a.controller.CaptchaController - 验证码信息 => CaptchaVo(captchaEnabled=true, uuid=d2273b5d730648389093deea79e4bd1e, img=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, tenantEnabled=false, key=047c42d2c4e28f54559b9a44f6b251dc5e35d70315a97d9bc3637fa4d2190021be49f95da908c62984004489893c5afa2120f8634ff35056e788bf05f4883d9f27, privateKey=null)
2025-06-08 08:52:26 [undertow-1] INFO  o.d.a.controller.AppTokenController - 接收到app登录的请求:{"userName":"8f10943604c1c02c4405710083457ca7de15062231bdb31387237a58a7724ab6dcab9da94e65da78a752c552c1fe475d1f408bc319a7deff7cfee9f46ee4cdcffbcfa362ac41ba950b3c2130e2edbf2d38bb83d67752415aeaa02a440c497874eb1822","password":"5bbed6814e4f0e58b92b876d53b3cf079d852d621645b7b35b796736147a023f921ecb46c1cdd0ed2b0604a957f762d1e6bd88f68d3c047769150b5920faa435b30624ba36c74202af8f94cff47f42b49b39fba944373ea587c66e20361a5d0e316336d275e6","uuid":"d2273b5d730648389093deea79e4bd1e","code":"1","type":"1","systemType":"aea","grantType":"password","clientId":"428a8310cd442757ae699df5d894f051"}
2025-06-08 08:52:26 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByAppClientId]
2025-06-08 08:52:28 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByAppClientId],SpendTime=[1484ms]
2025-06-08 08:52:28 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[getAppUserInfo]
2025-06-08 08:52:28 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[getAppUserInfo],SpendTime=[449ms]
2025-06-08 08:52:28 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned]
2025-06-08 08:52:28 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned],SpendTime=[16ms]
2025-06-08 08:52:28 [undertow-1] INFO  o.d.auth.service.AppLoginService - 获取用户登录错误次数为：null
2025-06-08 08:52:28 [undertow-1] INFO  o.d.auth.service.AppLoginService - 人脸识别结果：true
2025-06-08 08:52:28 [undertow-1] INFO  o.d.auth.service.AppLoginService - 恭喜您登录成功，清除该用户登录错误次数
2025-06-08 08:52:28 [undertow-1] INFO  o.d.a.s.impl.AppPasswordStrategy - SaLoginModel [device=android, isLastingCookie=true, timeout=60480000, activeTimeout=18000000, extraData={clientid=428a8310cd442757ae699df5d894f051}, token=null, isWriteHeader=null, tokenSignTag=null]
2025-06-08 08:52:29 [async-0] INFO  o.d.c.log.event.LogEventListener - [************]内网IP[wtc][Success][登录成功]
2025-06-08 08:52:29 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo]
2025-06-08 08:52:29 [async-0] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor]
2025-06-08 08:52:29 [async-0] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor],SpendTime=[31ms]
2025-06-08 08:52:29 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo],SpendTime=[63ms]
2025-06-08 08:52:29 [undertow-1] INFO  o.d.auth.listener.UserActionListener - app user doLogin, useId:app_user:1929882877257396226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6Ik40VXFpVXFPdGVZWU4xT3B4NEJITHM1aHV6bGs4UW1BIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.FmRfxPvF3BXdlB0Fpt5FC4UuTuYN4Fwo6iphpEq1tm0
2025-06-08 08:52:29 [undertow-1] INFO  o.d.auth.listener.UserActionListener - app user doLogout, useId:app_user:1929882877257396226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6InNTVERDdG9NOURkYVhScUFCT0diVlRrWXdtajM1dERxIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.XLdM-zku-oExtFf4WcYQcI8t6jkWhm9aCghwFspkfnY
2025-06-08 08:52:29 [async-1] INFO  o.d.c.log.event.LogEventListener - [************]内网IP[wtc][Success][登录成功]
2025-06-08 08:52:29 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo]
2025-06-08 08:52:29 [async-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor]
2025-06-08 08:52:29 [async-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-08 08:52:29 [undertow-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo],SpendTime=[114ms]
2025-06-08 08:52:29 [undertow-1] INFO  o.d.auth.listener.UserActionListener - app user doLogin, useId:app_user:1929882877257396226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6Ik40VXFpVXFPdGVZWU4xT3B4NEJITHM1aHV6bGs4UW1BIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.FmRfxPvF3BXdlB0Fpt5FC4UuTuYN4Fwo6iphpEq1tm0
2025-06-08 08:52:29 [undertow-1] INFO  o.d.a.s.impl.AppPasswordStrategy - 登录成功，返回前端数据：AppLoginVo(accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6Ik40VXFpVXFPdGVZWU4xT3B4NEJITHM1aHV6bGs4UW1BIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.FmRfxPvF3BXdlB0Fpt5FC4UuTuYN4Fwo6iphpEq1tm0, refreshToken=null, expireIn=60479999, refreshExpireIn=null, clientId=428a8310cd442757ae699df5d894f051, scope=null, openid=null, user=null)
2025-06-08 08:52:29 [undertow-1] INFO  o.d.a.controller.AppTokenController - 用户信息：AppLoginVo(accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6Ik40VXFpVXFPdGVZWU4xT3B4NEJITHM1aHV6bGs4UW1BIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.FmRfxPvF3BXdlB0Fpt5FC4UuTuYN4Fwo6iphpEq1tm0, refreshToken=null, expireIn=60479999, refreshExpireIn=null, clientId=428a8310cd442757ae699df5d894f051, scope=null, openid=null, user=null)
2025-06-08 08:52:32 [schedule-pool-1] INFO  o.a.d.r.c.ServiceDiscoveryRegistry -  [DUBBO] Trying to subscribe from apps cyly-resource,ruoyi-resource for service key org.dromara.resource.api.RemoteMessageService, , dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:32 [schedule-pool-1] INFO  o.a.d.r.c.e.l.ServiceInstancesChangedListener -  [DUBBO] Notify serviceKey: org.dromara.resource.api.RemoteMessageService:null, listener: ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [cyly-resource, ruoyi-resource])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]) with 1 urls on subscription, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:32 [schedule-pool-1] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Refreshed invoker size 1 from registry ServiceDiscoveryRegistryDirectory(registry: *************:8848, subscribed key: [cyly-resource, ruoyi-resource])-Directory(invokers: 0[], validInvokers: 0[], invokersToReconnect: 0[]), dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:32 [schedule-pool-1] INFO  o.a.d.r.c.ServiceDiscoveryRegistryDirectory -  [DUBBO] Received invokers changed event from registry. Registry type: instance. Service Key: org.dromara.resource.api.RemoteMessageService. Urls Size : 1. Invokers Size : 1. Available Size: 1. Available Invokers : ***********:20880, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:32 [schedule-pool-1] INFO  o.a.d.r.c.m.MigrationRuleHandler -  [DUBBO] Succeed Migrated to FORCE_APPLICATION mode. Service Name: org.dromara.resource.api.RemoteMessageService, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:32 [schedule-pool-1] INFO  o.a.dubbo.config.ReferenceConfig -  [DUBBO] Referred dubbo service: [org.dromara.resource.api.RemoteMessageService]. it's not GenericService reference, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:32 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-auth', serviceInterface='org.dromara.resource.api.RemoteMessageService', version='', group='', side='consumer'}; definition: {application=cyly-auth, pid=162648, side=consumer, dubbo=2.0.2, metadata-type=remote, release=3.3.4, interface=org.dromara.resource.api.RemoteMessageService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=publishAll,publishMessage, logger=slf4j, check=false, qos.enable=false, timeout=5000, register-mode=instance, unloadClusterRelated=false, retries=0, background=false, stub=true, sticky=false, validation=jvalidationNew, timestamp=1749343952563}, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:32 [Dubbo-framework-mapping-refreshing-scheduler-thread-1] INFO  o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener -  [DUBBO] Received mapping notification from meta server, {serviceKey: org.dromara.resource.api.RemoteMessageService, apps: [cyly-resource]}, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:32 [Dubbo-framework-mapping-refreshing-scheduler-thread-1] INFO  o.a.d.r.c.ServiceDiscoveryRegistry$DefaultMappingListener -  [DUBBO] Mapping of service org.dromara.resource.api.RemoteMessageServicechanged from [ruoyi-resource, cyly-resource] to [cyly-resource], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:32 [schedule-pool-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMessageService],MethodName=[publishMessage]
2025-06-08 08:52:33 [schedule-pool-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteMessageService],MethodName=[publishMessage],SpendTime=[831ms]
2025-06-08 08:52:45 [undertow-3] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:************:'
2025-06-08 08:52:45 [undertow-3] INFO  o.d.a.controller.CaptchaController - 验证码信息 => CaptchaVo(captchaEnabled=true, uuid=96839e40a8d04db0ae976b151dcf40c4, img=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, tenantEnabled=false, key=04e7d66c70d28813db51583f05650b0b7b5d69c51712feac408088bc5fb5c6bee30db1aeebd7ecd800606c391e898cd53630fc98437e10ce49a420448046a0a4f1, privateKey=null)
2025-06-08 08:52:45 [undertow-5] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 8, 缓存key => 'global:rate_limit:/code:************:'
2025-06-08 08:52:45 [undertow-5] INFO  o.d.a.controller.CaptchaController - 验证码信息 => CaptchaVo(captchaEnabled=true, uuid=218de58d6b834c5f9f7305da6bcdaa5a, img=iVBORw0KGgoAAAANSUhEUgAAAKAAAAA8CAYAAADha7EVAAAJyElEQVR4Xu2cS3AcRxnHu6jikAvYAZuY4lFJFRQEwoGQhEDFhAMUBeFRZTnEjh3j+FHECQYnTiU2xEXKtuTYkpFiGz+iYMfWrrTSWrKlfWi1L1e4cIACLhw4cIUrp5yoZr9RPqX1757Z6dmZ0a7Uh19p9+uvu1Xyzz3dPT0j7ty5Ix2OlUJgwOFIE4EBhyNNBAYcjjQRGHA40kRgwOFIE4EBhyNNBAYcjjQRGHB0Pxv+83cNzOkVBAYc3UuQbEFlnXLwm3+zBtvwQ2DA0Z2ElStsng0oV1iwHRMCA8hMpiyHDo7JX20+L3d/flA+9YkBjz1fHJKHHr8gRw5nZWG6otVbyxz6yDtL1Bo1ObcwK6fnp2SulJXjxTGZKVz3fk4Us/JmeUoWqnNeHrbD2Eplm7+SCAwwc1MVefTHl2Xf+pNyy0dPBNK3/oQ8vuOqXCjXtXbWGiQd/Ww06y3p8nJs9ronXBhmKje9etimrVC2+SuJwAAxdu62fPrTb2iitWPXfWfk9I2S1t5aggRcqFdao1tGEywME6WMrDYWltqLKlPUemkjMEDybf1Y+1HPjx2feUPmrxW1jtYCi5fc6tJlNirjLXnpktypRJ3WTwOhfqHL7tOfOqVJZcuue8/I+dmq1tlqhwScKuc0oaKQb7XTqUCd1k8DoX45+qPLmkxR+fVPrmidrXZ++/g7mkgqN1sLkWqD/mM2vZGSvmOOyuZ//lHrw4aeEpBWu0ELjr33D8nR/rycn6vK+kJDTo4WPMkwj6G2ZsbKWodJ8u6+I0YwLw5yB+oa2ct5TSJmsjSutUFMlia0XObgnwpavi3dLqHgD7TVghIxz35hUC6U9NUZceHohJbP0MoY85MExYtbQFU2LPPKS1lNIqZcMy/OKI65TM5HWht6RsBD3zqvCcRcPDapVVR59YlLWh1i66YB2Wzq+UmB4sUpIEl35S/3aXGVbPGGJhFTb5r3+SiOuQwtZjDflp4RkDaZUSCGFidYUeWZnb/X6jC79l3S8pMCxYtLQJavnYCZgr+ANO/DfKLZimMuk221h/m2sIB3/fW9RMF+wyL4w7Z7BjR5mEbN/Mcjdr8yKiuFmlaHOX9kwsvBekmA4sUhIF9uwwgYdAnOHTb/Jw4aAak9zEceKew3wuU9I+CTH/dfgNRaiw6syJBcJCjWYV7f/oeeFVCd64UR8FZlWpOIma+XjXPHUrWo5TLUHuYzKBrC5T0j4DP3ntbkYaavmyfQLBZdorEO8+K3LyzLTRIUL20B6Q4GSsTQatckIK2OMZcYa80nF7ds9H6CxEM4F4WJG+w3LII/HHhoRJOHGfqFeTLMUr35clarw+x/4OyyXObRD/1MA9u3BcVLW0AiaBTMjk55+3/N5vv7gGX/fcA9f57X2iZYqPyxB4xgPo2AVAeFiRvsNyyCP/Q/e02Th9n+yVPGPT2SajY3L3d+1n/0pJGVc7men2x+8bCgeCshIC026OQLCmUDHUrY+G/9OBOJ5Ccao5arK2AUJm7w9wiL4A/jF+c0eVDC4Rcz3uWW5nzF6QW544k32x5a+OnGfq/9TgVEqeIG+2NsFiEMjXB0xIpWsShXEHQPuNiqx+2oArF82JcflItbMDaX7rQQ6peD3zinCdQpdLCB2u70EozCxA32p6JKiGVBFKsFTTJ/bnj5an1VIBv5/Op0m4DH1v1ruYC5K3Ot1XC/JlEn0C05lC8KKEzcYH+IaQHhBx3HorsYumTtoUUJ1ad2WEASKYo8VF+VMEobSUHy0U+BBUG31qJA2zurQUCi3a04olQrysys3aXXBI+GLJGtPKq8HLNtIylYPkJgIXH+1XG59W7/fUGNjSfly9+9qMdb9G1YvAR3CgoTN9hfO1QZmYnflGVmTpeJoNtqdDSfzvnRHJE2oGcrt3zniXRbj/YOeS5nIw/O/agNXDTEDf4OfqjyEQITGFqUPPfVYU0o5LkHh71T0Ns3n9XKiN2fO6O1HQUUJm6wP1tIKnrGA0ViKnXztkq5VtZyGTpVPfX6g0tChZWwWwVE+QiBARU6SHBtcEYe6xuVe780JLdtGpBPbuj3pHrl+5fk6Mm8bDYWb9Md+aH5LOGBr41o7SYFShWXXGEIuqMxM39Ty1ehcqzD5C7s8nLCSojyET0roA3PP2zeyCZRMTcpULy4BfzeQN6XoMOlfqMfQ+VYhxnPHl/KI7n8LsdchnHevkFh4gb7NZGogLRPiPIRA3sXnxJLAxQvLgFZMoyrjM6Y53IEnXjBfJVGs6HVWWLmspbPEhJB4rGo3SCgST5CYCAKtzJlTTzm7Tb/cHGC4sUhYDvxmKCzgO0EJLAOQ4sU3M9TYdEQLue6KEzc4O+FhBLw6ulp777uwJ5r3nH7Xz52bnHud89A4ENGwy9lNPGIvnUnZGnmg0cMkwbFayfg6TPrloHlYeUjgp6EMz3rq0LlWIehuyNBArajk7pxEkpAkg0lWhrJTpkn0s3GHbnvy+YVMK2QMT9JULwgAU3CqTEb+Qi/Uy3EfE2/j64SNAfkZ0miiBSlTlKEEjDoIaMXvj6ytOJVodESc5nhl7JafpKgeH4CmuTDMlsB6S0IKA9Dp14wX4UOH2AdRj0TaCOUTW4ahBLwrRN5TSIVej8MPQ1XrdS9ed9rW97Scphtm055J6WxwyRB8aIKaCsfQaMcyqNC2zRYh6DRL2j+iCtoEitIrnblK0UoAelxS7/VrC1+ZwiTBMUzCRgkn02OTjPwSD5JdrsyI2vNxTsh3kuLqrMy6DkSuvxSrt7XB6IhmNdNmCQUGBh8YUyTyZbnHxmRjbr5D7fShJErTI6JSr2iSdQJOPr1OqEEpFFw/1d+p0kVFnptW+lWeitfW8LIFSbHDzoHiCJFgdrBtnudUAISdOj0B/dbHEZ4Hxr5ulk+IoxcYXKCoPle0LwuGDoXuPrkY1BCgQnE+vfOevt+h7/j/7yvCs0b6c0K3XrZRYIEi7oKRrYMTnorY7/TLiby5cllr2ZbrQQexyL51O+Tbxfk8Z1XvZMxdPyejmk9tXHxDamP7RiQF1/Lyep88EZrt5GGgFyfbrPRiEZbLbni8jek0qKFpJtbuO37BNxqhSUUWIACBmGT222YJMRYVAmj1ltrkIQCgzZS2eR2IyScCpYTtjLZ5q91BAZspLLJ7WXCnIYJk+PQERiwkcomdzXAkpnAXEc4BAaIMGKFyXE42iEwwJBgJsn84g5HFAQGEBbOiedIAoEBhyNNBAYcjjQRGHA40kRgwNH9/Lz+Xw3M+fA//mcE81YagQFHd2OSDeNBogWVrQQCA47uxU8+tTyMYGFy0kJgwNF9+F1mTbSTMKhsJfg/FZ9clahSeyQAAAAASUVORK5CYII=, tenantEnabled=false, key=041b8d9b9b1ec6935f4b491ad2ed69de093a9e77a4ef668d7ea55e714523e51435bdbbc269e0d4c76c71bfc0489cebebbcb060c85925dc59bb497b07770579d121, privateKey=null)
2025-06-08 08:53:22 [undertow-7] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 7, 缓存key => 'global:rate_limit:/code:************:'
2025-06-08 08:53:22 [undertow-7] INFO  o.d.a.controller.CaptchaController - 验证码信息 => CaptchaVo(captchaEnabled=true, uuid=5a5958d9350643c58998a91fd289fce4, img=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, tenantEnabled=false, key=045667cc8bfe5cb11e1aa96c042397129bb361a14c1685ae93b8ed767910867de39fb1a0c94f4cff11024e8737e079620997e952811b61d22924963acf3a0f781e, privateKey=null)
2025-06-08 08:53:27 [undertow-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-08 08:53:27 [undertow-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[14ms]
2025-06-08 08:53:27 [undertow-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo]
2025-06-08 08:53:27 [undertow-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[getUserInfo],SpendTime=[57ms]
2025-06-08 08:53:27 [undertow-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned]
2025-06-08 08:53:27 [undertow-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned],SpendTime=[8ms]
2025-06-08 08:53:27 [async-2] INFO  o.d.c.log.event.LogEventListener - [************]内网IP[admin][Success][登录成功]
2025-06-08 08:53:27 [async-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId]
2025-06-08 08:53:27 [undertow-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo]
2025-06-08 08:53:27 [async-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByClientId],SpendTime=[4ms]
2025-06-08 08:53:27 [undertow-9] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteUserService],MethodName=[recordLoginInfo],SpendTime=[16ms]
2025-06-08 08:53:27 [async-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor]
2025-06-08 08:53:27 [undertow-9] INFO  o.d.auth.listener.UserActionListener - sys user doLogin, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiJwRFJ6WGRUcDdZTVREMWlGTVl2b2dtTVozWVdMc1M1byIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTA2LCJkZXB0TmFtZSI6IiIsInVzZXJUeXBlIjoic3lzX3VzZXIiLCJuaWNrTmFtZSI6IueWr-eLgueahOeLruWtkExpIn0._F2luKCdwgtRbLSE-6QyyrBmdz48CMFGP6wSTLqa59U
2025-06-08 08:53:27 [async-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteLogService],MethodName=[saveLogininfor],SpendTime=[6ms]
2025-06-08 08:53:27 [undertow-9] INFO  o.d.auth.listener.UserActionListener - user doLogout, useId:sys_user:1, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiI5MGVDU2lwNm52UjdYN1dTcU1URmhyOWhsRDJjNGI5SiIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTA2LCJkZXB0TmFtZSI6IiIsInVzZXJUeXBlIjoic3lzX3VzZXIiLCJuaWNrTmFtZSI6IueWr-eLgueahOeLruWtkExpIn0.36NN1lA6A6QxyA4MQ_RVKpiaSXW7gnSSEGPjQ1vtnPQ
2025-06-08 08:53:30 [schedule-pool-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMessageService],MethodName=[publishMessage]
2025-06-08 08:53:30 [schedule-pool-1] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteMessageService],MethodName=[publishMessage],SpendTime=[12ms]
2025-06-08 09:34:55 [undertow-11] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:************:'
2025-06-08 09:34:55 [undertow-11] INFO  o.d.a.controller.CaptchaController - 验证码信息 => CaptchaVo(captchaEnabled=true, uuid=021dd8b3fe8e4188b2e40c65a65184a4, img=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, tenantEnabled=false, key=04735c5670480e8fe25546968abf30c574d2b06bc2452eac13320950633a294b42ae9f973a0fe93abbaff8e3f0da08e646163b609c7ec7b16184ad2bcb21c68519, privateKey=null)
2025-06-08 09:36:03 [undertow-12] INFO  o.d.c.r.aspectj.RateLimiterAspect - 限制令牌 => 10, 剩余令牌 => 9, 缓存key => 'global:rate_limit:/code:************:'
2025-06-08 09:36:03 [undertow-12] INFO  o.d.a.controller.CaptchaController - 验证码信息 => CaptchaVo(captchaEnabled=true, uuid=edea39a14f1842cfbf41f103e7a93507, img=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, tenantEnabled=false, key=043fac75bfcbae2daf1824414eacd2e1ab4c5bf8ad1eca9b02d4784cdd50d045aed22db90076583ba1b74517013f5d42136097e75c33ed9bd9f9970ce49bc02cee, privateKey=null)
2025-06-08 09:36:14 [undertow-13] INFO  o.d.a.controller.AppTokenController - 接收到app登录的请求:{"userName":"092d9abcf2610c0484b0ffd44702d121769652e2c71baa167712900a87e43c912c09ef19e5a4edbadb18b6964f54f38522358fc79425b1f2a3ddafd8eb802d850bbb3943757e7cbc3c2806c961b992a5381aee5d633f47a38776efe1459d2fa648cfbe","password":"7b7eaedfc4cf6f8b66662dea7ce0d3491c6867ff043160079f6189bc26a2969b8ca399c8ed142b16b5d0922d24b0d4c6e49926e29d78911347d1ace24dd77267ad58217b9d19a1fb168430af26834571f5f6d7d6e449cbfd15a7ddee27f4ab5b7590c7ed75f2","uuid":"edea39a14f1842cfbf41f103e7a93507","code":"10","type":"1","systemType":"aea","grantType":"password","clientId":"428a8310cd442757ae699df5d894f051"}
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByAppClientId]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteClientService],MethodName=[queryByAppClientId],SpendTime=[125ms]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[getAppUserInfo]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[getAppUserInfo],SpendTime=[41ms]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteIpBanService],MethodName=[isNetworkBanned],SpendTime=[14ms]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.auth.service.AppLoginService - 获取用户登录错误次数为：null
2025-06-08 09:36:14 [undertow-13] INFO  o.d.auth.service.AppLoginService - 人脸识别结果：true
2025-06-08 09:36:14 [undertow-13] INFO  o.d.auth.service.AppLoginService - 恭喜您登录成功，清除该用户登录错误次数
2025-06-08 09:36:14 [undertow-13] INFO  o.d.a.s.impl.AppPasswordStrategy - SaLoginModel [device=android, isLastingCookie=true, timeout=60480000, activeTimeout=18000000, extraData={clientid=428a8310cd442757ae699df5d894f051}, token=null, isWriteHeader=null, tokenSignTag=null]
2025-06-08 09:36:14 [async-3] INFO  o.d.c.log.event.LogEventListener - [************]内网IP[wtc][Success][登录成功]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo]
2025-06-08 09:36:14 [async-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor]
2025-06-08 09:36:14 [async-3] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor],SpendTime=[6ms]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo],SpendTime=[120ms]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.auth.listener.UserActionListener - app user doLogin, useId:app_user:1929882877257396226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6IjRuakZnVmZ1dTduNWliZEFOYnJCYkJtZXVrTzFZRnBzIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.g9B_x9AUioGx7ZMT8yGZiV49Oj5ohCT2b8BgDhJAty0
2025-06-08 09:36:14 [undertow-13] INFO  o.d.auth.listener.UserActionListener - app user doLogout, useId:app_user:1929882877257396226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6InhQUGpsWWRzcTVaRlppN2FFeGcwY2EwWmxpN21ZTlZNIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.xP1t5-fOezagcCq5upcUi_ch4N0TtdPGv-8wbWm95iY
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo]
2025-06-08 09:36:14 [async-4] INFO  o.d.c.log.event.LogEventListener - [************]内网IP[wtc][Success][登录成功]
2025-06-08 09:36:14 [async-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor]
2025-06-08 09:36:14 [async-4] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppLogService],MethodName=[saveLogininfor],SpendTime=[4ms]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteAppUserService],MethodName=[recordLoginInfo],SpendTime=[42ms]
2025-06-08 09:36:14 [undertow-13] INFO  o.d.auth.listener.UserActionListener - app user doLogin, useId:app_user:1929882877257396226, token:eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6IjRuakZnVmZ1dTduNWliZEFOYnJCYkJtZXVrTzFZRnBzIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.g9B_x9AUioGx7ZMT8yGZiV49Oj5ohCT2b8BgDhJAty0
2025-06-08 09:36:14 [undertow-13] INFO  o.d.a.s.impl.AppPasswordStrategy - 登录成功，返回前端数据：AppLoginVo(accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6IjRuakZnVmZ1dTduNWliZEFOYnJCYkJtZXVrTzFZRnBzIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.g9B_x9AUioGx7ZMT8yGZiV49Oj5ohCT2b8BgDhJAty0, refreshToken=null, expireIn=60479999, refreshExpireIn=null, clientId=428a8310cd442757ae699df5d894f051, scope=null, openid=null, user=null)
2025-06-08 09:36:14 [undertow-13] INFO  o.d.a.controller.AppTokenController - 用户信息：AppLoginVo(accessToken=eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJ1c2VyIiwibG9naW5JZCI6ImFwcF91c2VyOjE5Mjk4ODI4NzcyNTczOTYyMjYiLCJyblN0ciI6IjRuakZnVmZ1dTduNWliZEFOYnJCYkJtZXVrTzFZRnBzIiwiY2xpZW50aWQiOiI0MjhhODMxMGNkNDQyNzU3YWU2OTlkZjVkODk0ZjA1MSIsInRlbmFudElkIjoiMDAwMDAwIiwidXNlcklkIjoxOTI5ODgyODc3MjU3Mzk2MjI2LCJ1c2VyTmFtZSI6Ind0YyIsInVzZXJUeXBlIjoiYXBwX3VzZXIiLCJ1c2VyUGhvbmVudW1iZXIiOiIxODg3Njc2NDUxMSJ9.g9B_x9AUioGx7ZMT8yGZiV49Oj5ohCT2b8BgDhJAty0, refreshToken=null, expireIn=60479999, refreshExpireIn=null, clientId=428a8310cd442757ae699df5d894f051, scope=null, openid=null, user=null)
2025-06-08 09:36:17 [schedule-pool-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[consumer],InterfaceName=[RemoteMessageService],MethodName=[publishMessage]
2025-06-08 09:36:17 [schedule-pool-2] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[consumer],InterfaceName=[RemoteMessageService],MethodName=[publishMessage],SpendTime=[183ms]
