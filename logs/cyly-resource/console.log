2025-06-08 02:33:04 [DubboMetadataReportTimer-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBB<PERSON>] start to publish all metadata., dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.resource.api.RemoteMailService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={anyhost=true, pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=provider, interface=org.dromara.resource.api.RemoteMailService, executor-management-mode=isolation, file-cache=true, methods=send, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20880, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311078631}} ServiceDefinition [canonicalName=org.dromara.resource.api.RemoteMailService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-resource/target/classes/, methods=[MethodDefinition [name=send, parameterTypes=[java.lang.String, java.lang.String, java.lang.String], returnType=void]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.aea.api.RemoteElderService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.aea.api.RemoteElderService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=getElder,getElderByNameAndIdCard,getElderList,getElderTaskInfoById,selectElderNameById,selectList,selectListByIds, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081738}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.system.api.RemoteUserService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.system.api.RemoteUserService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=checkEmailUnique,checkPhoneUnique,checkUserAllowed,checkUserDataScope,checkUserNameUnique,deleteUserByIds,faceRecognition,getUserInfo,getUserInfoByEmail,getUserInfoByOpenid,getUserInfoByPhoneNumber,insertAppUser,insertUser,recordLoginInfo,registerUserInfo,selectEmailById,selectListByIds,selectNicknameById,selectNicknameByIds,selectPageUserList,selectPhoneNumberById,selectUserById,selectUserIdsByRoleIds,selectUserNameById,updateBatchById,updateUserInfo,updateUserStatus,userAssessorCount, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311080939}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.system.api.RemoteDictService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.system.api.RemoteDictService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=selectDictDataByType, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311082144}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.system.api.RemoteAppLogService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.system.api.RemoteAppLogService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=saveLog,saveLogininfor, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081336}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.system.api.RemoteClientService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.system.api.RemoteClientService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=queryByAppClientId,queryByClientId, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311080748}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.resource.api.RemoteCheckOssService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.resource.api.RemoteCheckOssService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=CheckOssAccess,CheckOssDelete,CheckOssDeleteByIds, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081944}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.system.api.RemoteDataScopeService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.system.api.RemoteDataScopeService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=getDeptAndChild,getRoleCustom, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081538}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.resource.api.RemoteSmsService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={anyhost=true, pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=provider, interface=org.dromara.resource.api.RemoteSmsService, executor-management-mode=isolation, file-cache=true, methods=addBlacklist,delayMessage,delayMessageTexting,messageTexting,removeBlacklist,sendMessage,sendMessageAsync, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20880, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311079695}} ServiceDefinition [canonicalName=org.dromara.resource.api.RemoteSmsService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-resource/target/classes/, methods=[MethodDefinition [name=sendMessage, parameterTypes=[java.lang.String, java.lang.String, java.util.LinkedHashMap<java.lang.String,java.lang.String>], returnType=org.dromara.resource.api.domain.RemoteSms], MethodDefinition [name=sendMessage, parameterTypes=[java.lang.String, java.util.LinkedHashMap<java.lang.String,java.lang.String>], returnType=org.dromara.resource.api.domain.RemoteSms], MethodDefinition [name=sendMessage, parameterTypes=[java.lang.String, java.lang.String], returnType=org.dromara.resource.api.domain.RemoteSms], MethodDefinition [name=delayMessageTexting, parameterTypes=[java.util.List<java.lang.String>, java.lang.String, java.util.LinkedHashMap<java.lang.String,java.lang.String>, java.lang.Long], returnType=void], MethodDefinition [name=delayMessageTexting, parameterTypes=[java.util.List<java.lang.String>, java.lang.String, java.lang.Long], returnType=void], MethodDefinition [name=sendMessageAsync, parameterTypes=[java.lang.String, java.lang.String], returnType=void], MethodDefinition [name=sendMessageAsync, parameterTypes=[java.lang.String, java.lang.String, java.util.LinkedHashMap<java.lang.String,java.lang.String>], returnType=void], MethodDefinition [name=messageTexting, parameterTypes=[java.util.List<java.lang.String>, java.lang.String, java.util.LinkedHashMap<java.lang.String,java.lang.String>], returnType=org.dromara.resource.api.domain.RemoteSms], MethodDefinition [name=messageTexting, parameterTypes=[java.util.List<java.lang.String>, java.lang.String], returnType=org.dromara.resource.api.domain.RemoteSms], MethodDefinition [name=delayMessage, parameterTypes=[java.lang.String, java.lang.String, java.util.LinkedHashMap<java.lang.String,java.lang.String>, java.lang.Long], returnType=void], MethodDefinition [name=delayMessage, parameterTypes=[java.lang.String, java.lang.String, java.lang.Long], returnType=void], MethodDefinition [name=removeBlacklist, parameterTypes=[java.util.List<java.lang.String>], returnType=void], MethodDefinition [name=removeBlacklist, parameterTypes=[java.lang.String], returnType=void], MethodDefinition [name=addBlacklist, parameterTypes=[java.lang.String], returnType=void], MethodDefinition [name=addBlacklist, parameterTypes=[java.util.List<java.lang.String>], returnType=void]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.resource.api.RemoteFileService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={anyhost=true, pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=provider, interface=org.dromara.resource.api.RemoteFileService, executor-management-mode=isolation, file-cache=true, methods=selectByIds,selectUrlByIds,upload,uploadBase64Image, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20880, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311079732}} ServiceDefinition [canonicalName=org.dromara.resource.api.RemoteFileService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-resource/target/classes/, methods=[MethodDefinition [name=upload, parameterTypes=[java.io.File], returnType=org.dromara.resource.api.domain.RemoteFile], MethodDefinition [name=upload, parameterTypes=[org.springframework.web.multipart.MultipartFile], returnType=org.dromara.resource.api.domain.RemoteFile], MethodDefinition [name=upload, parameterTypes=[java.lang.String, java.lang.String, java.lang.String, byte[]], returnType=org.dromara.resource.api.domain.RemoteFile], MethodDefinition [name=uploadBase64Image, parameterTypes=[java.lang.String], returnType=org.dromara.resource.api.domain.RemoteFile], MethodDefinition [name=selectUrlByIds, parameterTypes=[java.lang.String], returnType=java.lang.String], MethodDefinition [name=selectByIds, parameterTypes=[java.lang.String], returnType=java.util.List<org.dromara.resource.api.domain.RemoteFile>]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.resource.api.RemoteFileService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.resource.api.RemoteFileService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=selectByIds,selectUrlByIds,upload,uploadBase64Image, logger=slf4j, check=false, qos.enable=false, timeout=15000, register-mode=instance, unloadClusterRelated=false, retries=0, background=false, sticky=false, mock=true, validation=jvalidationNew, timestamp=1749311080393}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store provider metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.resource.api.RemoteMessageService', version='', group='', side='provider'}; definition: FullServiceDefinition{parameters={anyhost=true, pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=provider, interface=org.dromara.resource.api.RemoteMessageService, executor-management-mode=isolation, file-cache=true, methods=publishAll,publishMessage, logger=slf4j, deprecated=false, service-name-mapping=true, register-mode=instance, qos.enable=false, generic=false, bind.port=20880, bind.ip=***********, prefer.serialization=hessian2,fastjson2, background=false, dynamic=true, timestamp=1749311079653}} ServiceDefinition [canonicalName=org.dromara.resource.api.RemoteMessageService, codeSource=file:/H:/KangYang_work/cloud-project/cyly-cloud-refactor/cyly-api/cyly-api-resource/target/classes/, methods=[MethodDefinition [name=publishAll, parameterTypes=[java.lang.String], returnType=void], MethodDefinition [name=publishMessage, parameterTypes=[java.lang.Long, java.lang.String], returnType=void]]], dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.system.api.RemoteDeptService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.system.api.RemoteDeptService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=checkDeptDataScope,selectBatchIds,selectById,selectDeptNameByIds, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311079764}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.system.api.RemoteLogService', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.system.api.RemoteLogService, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=saveLog,saveLogininfor, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311081140}, dubbo version: 3.3.4, current host: ***********
2025-06-08 02:33:04 [DubboSaveMetadataReport-thread-1] INFO  o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] store consumer metadata. Identifier : MetadataIdentifier{application='cyly-resource', serviceInterface='org.dromara.xchat.api.model.RemoteNeteaseClient', version='', group='', side='consumer'}; definition: {pid=72332, metadata-type=remote, application=cyly-resource, dubbo=2.0.2, release=3.3.4, side=consumer, interface=org.dromara.xchat.api.model.RemoteNeteaseClient, executor-management-mode=isolation, file-cache=true, cache=false, register.ip=***********, methods=addFriend,refreshUserToken,register,sendVerificationCode, logger=slf4j, check=false, register-mode=instance, qos.enable=false, timeout=3000, unloadClusterRelated=false, retries=0, background=false, sticky=false, validation=jvalidationNew, timestamp=1749311080598}, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:23:06 [NettyServerWorker-5-5] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x23973c08, L:/***********:20880 ! R:/***********:51620] of ***********:51620 -> ***********:20880 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:23:45 [NettyServerWorker-5-6] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x91194e15, L:/***********:20880 - R:/***********:51979] of ***********:20880 -> ***********:51979 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:30:45 [NettyServerWorker-5-6] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] IdleStateEvent triggered, close channel NettyChannel [channel=[id: 0x91194e15, L:/***********:20880 - R:/***********:51979]], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:30:45 [NettyServerWorker-5-6] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x91194e15, L:/***********:20880 - R:/***********:51979], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:30:45 [NettyServerWorker-5-6] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x91194e15, L:/***********:20880 ! R:/***********:51979] of ***********:51979 -> ***********:20880 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:34:00 [NettyServerWorker-5-7] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0xfd84a789, L:/***********:20880 - R:/***********:53008] of ***********:20880 -> ***********:53008 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:35:52 [NettyServerWorker-5-7] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0xfd84a789, L:/***********:20880 ! R:/***********:53008] of ***********:53008 -> ***********:20880 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:36:18 [NettyServerWorker-5-8] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x012d96fb, L:/***********:20880 - R:/***********:53319] of ***********:20880 -> ***********:53319 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:44:34 [NettyServerWorker-5-8] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] IdleStateEvent triggered, close channel NettyChannel [channel=[id: 0x012d96fb, L:/***********:20880 - R:/***********:53319]], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:44:34 [NettyServerWorker-5-8] INFO  o.a.d.r.t.netty4.NettyChannel -  [DUBBO] Close netty channel [id: 0x012d96fb, L:/***********:20880 - R:/***********:53319], dubbo version: 3.3.4, current host: ***********
2025-06-08 08:44:34 [NettyServerWorker-5-8] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x012d96fb, L:/***********:20880 ! R:/***********:53319] of ***********:53319 -> ***********:20880 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:51:27 [NettyServerWorker-5-9] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0xcb9310dc, L:/***********:20880 - R:/***********:54844] of ***********:20880 -> ***********:54844 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:51:29 [NettyServerWorker-5-9] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0xcb9310dc, L:/***********:20880 ! R:/***********:54844] of ***********:54844 -> ***********:20880 is disconnected., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:07 [NettyServerWorker-5-10] INFO  o.a.d.r.t.netty4.NettyServerHandler -  [DUBBO] The connection [id: 0x5c73bc59, L:/***********:20880 - R:/***********:54996] of ***********:20880 -> ***********:54996 is established., dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:33 [NettyServerWorker-5-2] INFO  o.a.d.rpc.protocol.dubbo.DubboCodec -  [DUBBO] Because thread pool isolation is enabled on the dubbo protocol, the body can only be decoded on the io thread, and the parameter[decode.in.io.thread] will be ignored, dubbo version: 3.3.4, current host: ***********
2025-06-08 08:52:33 [DubboServerHandler-***********:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMessageService],MethodName=[publishMessage]
2025-06-08 08:52:33 [DubboServerHandler-***********:20880-thread-17] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1929882877257396226] message:欢迎长者智能照护系统
2025-06-08 08:52:33 [DubboServerHandler-***********:20880-thread-17] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteMessageService],MethodName=[publishMessage],SpendTime=[23ms]
2025-06-08 08:52:33 [redisson-2-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1929882877257396226] message=欢迎长者智能照护系统
2025-06-08 08:52:33 [DubboServerHandler-***********:20880-thread-17] WARN  i.m.core.instrument.MeterRegistry - This Gauge has been already registered (MeterId{name='sentinel.current.threads', tags=[tag(resource=org.dromara.resource.api.RemoteMessageService:publishMessage(java.lang.Long,java.lang.String))]}), the Gauge registration will be ignored. Note that subsequent logs will be logged at debug level.
2025-06-08 08:53:30 [DubboServerHandler-***********:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMessageService],MethodName=[publishMessage]
2025-06-08 08:53:30 [DubboServerHandler-***********:20880-thread-18] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1] message:欢迎登录RuoYi-Cloud-Plus微服务管理系统
2025-06-08 08:53:30 [DubboServerHandler-***********:20880-thread-18] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteMessageService],MethodName=[publishMessage],SpendTime=[3ms]
2025-06-08 08:53:30 [redisson-2-3] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录RuoYi-Cloud-Plus微服务管理系统
2025-06-08 09:36:17 [DubboServerHandler-***********:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务调用: Client[provider],InterfaceName=[RemoteMessageService],MethodName=[publishMessage]
2025-06-08 09:36:17 [DubboServerHandler-***********:20880-thread-19] INFO  o.d.c.sse.core.SseEmitterManager - SSE发送主题订阅消息topic:global:sse session keys:[1929882877257396226] message:欢迎长者智能照护系统
2025-06-08 09:36:17 [DubboServerHandler-***********:20880-thread-19] INFO  o.d.c.d.filter.DubboRequestFilter - DUBBO - 服务响应: Client[provider],InterfaceName=[RemoteMessageService],MethodName=[publishMessage],SpendTime=[18ms]
2025-06-08 09:36:17 [redisson-2-4] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1929882877257396226] message=欢迎长者智能照护系统
2025-06-08 10:27:42 [redisson-2-1] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1] message=欢迎登录RuoYi-Cloud-Plus微服务管理系统
2025-06-08 14:22:30 [redisson-2-2] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1930565187881349122] message=欢迎长者智能照护系统
2025-06-08 15:20:29 [redisson-2-3] INFO  o.d.c.sse.listener.SseTopicListener - SSE主题订阅收到消息session keys=[1929882877257396226] message=欢迎长者智能照护系统
2025-06-08 16:38:15 [Thread-34] ERROR o.a.d.m.s.redis.RedisMetadataReport -  [DUBBO] Failed to subscribe DUBBO_GROUP:mapping:queues, cause: java.net.SocketException: Connection reset, dubbo version: 3.3.4, current host: ***********, error code: 6-14. This may be caused by , go to https://dubbo.apache.org/faq/6/14 to find instructions. 
redis.clients.jedis.exceptions.JedisConnectionException: java.net.SocketException: Connection reset
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:251)
	at redis.clients.jedis.util.RedisInputStream.readByte(RedisInputStream.java:47)
	at redis.clients.jedis.Protocol.process(Protocol.java:135)
	at redis.clients.jedis.Protocol.read(Protocol.java:221)
	at redis.clients.jedis.Connection.readProtocolWithCheckingBroken(Connection.java:350)
	at redis.clients.jedis.Connection.getUnflushedObject(Connection.java:316)
	at redis.clients.jedis.JedisPubSubBase.process(JedisPubSubBase.java:115)
	at redis.clients.jedis.JedisPubSubBase.proceed(JedisPubSubBase.java:92)
	at redis.clients.jedis.Jedis.subscribe(Jedis.java:7941)
	at org.apache.dubbo.metadata.store.redis.RedisMetadataReport$MappingDataListener.run(RedisMetadataReport.java:497)
Caused by: java.net.SocketException: Connection reset
	at java.base/sun.nio.ch.NioSocketImpl.implRead(NioSocketImpl.java:318)
	at java.base/sun.nio.ch.NioSocketImpl.read(NioSocketImpl.java:346)
	at java.base/sun.nio.ch.NioSocketImpl$1.read(NioSocketImpl.java:796)
	at java.base/java.net.Socket$SocketInputStream.read(Socket.java:1099)
	at java.base/java.io.InputStream.read(InputStream.java:220)
	at redis.clients.jedis.util.RedisInputStream.ensureFill(RedisInputStream.java:245)
	... 9 common frames omitted
2025-06-08 16:38:16 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@1ef5dc77 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-08 16:38:16 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@c4a6187 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-08 16:38:16 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@23a5f825 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-08 16:38:17 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@77bff1f7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-08 16:38:24 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@20cffab6 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-08 16:38:28 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@73be5a7e (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-08 16:38:31 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@7b063db7 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-08 16:38:33 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@24c3622b (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-08 16:38:36 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6cd652bb (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
2025-06-08 16:38:39 [master housekeeper] WARN  com.zaxxer.hikari.pool.PoolBase - master - Failed to validate connection com.mysql.cj.jdbc.ConnectionImpl@6dba88f5 (No operations allowed after connection closed.). Possibly consider using a shorter maxLifetime value.
