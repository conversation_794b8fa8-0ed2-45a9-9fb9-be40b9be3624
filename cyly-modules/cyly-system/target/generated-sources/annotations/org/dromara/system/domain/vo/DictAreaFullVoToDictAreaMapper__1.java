package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.DictArea;
import org.dromara.system.domain.DictAreaToDictAreaFullVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {DictAreaToDictAreaFullVoMapper__1.class},
    imports = {}
)
public interface DictAreaFullVoToDictAreaMapper__1 extends BaseMapper<DictAreaFullVo, DictArea> {
}
