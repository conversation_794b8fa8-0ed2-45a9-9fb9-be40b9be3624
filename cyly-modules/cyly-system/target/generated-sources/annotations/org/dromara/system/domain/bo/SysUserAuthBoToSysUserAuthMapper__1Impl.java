package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysUserAuth;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T09:56:27+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysUserAuthBoToSysUserAuthMapper__1Impl implements SysUserAuthBoToSysUserAuthMapper__1 {

    @Override
    public SysUserAuth convert(SysUserAuthBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserAuth sysUserAuth = new SysUserAuth();

        sysUserAuth.setCreateBy( arg0.getCreateBy() );
        sysUserAuth.setCreateDept( arg0.getCreateDept() );
        sysUserAuth.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysUserAuth.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysUserAuth.setSearchValue( arg0.getSearchValue() );
        sysUserAuth.setUpdateBy( arg0.getUpdateBy() );
        sysUserAuth.setUpdateTime( arg0.getUpdateTime() );
        sysUserAuth.setAuthIds( arg0.getAuthIds() );
        sysUserAuth.setId( arg0.getId() );
        sysUserAuth.setUserId( arg0.getUserId() );

        return sysUserAuth;
    }

    @Override
    public SysUserAuth convert(SysUserAuthBo arg0, SysUserAuth arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAuthIds( arg0.getAuthIds() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
