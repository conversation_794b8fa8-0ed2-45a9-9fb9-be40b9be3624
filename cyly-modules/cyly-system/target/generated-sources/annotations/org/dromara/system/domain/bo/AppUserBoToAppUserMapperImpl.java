package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.AppUser;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:10+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppUserBoToAppUserMapperImpl implements AppUserBoToAppUserMapper {

    @Override
    public AppUser convert(AppUserBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUser appUser = new AppUser();

        appUser.setCreateBy( arg0.getCreateBy() );
        appUser.setCreateDept( arg0.getCreateDept() );
        appUser.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            appUser.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        appUser.setSearchValue( arg0.getSearchValue() );
        appUser.setUpdateBy( arg0.getUpdateBy() );
        appUser.setUpdateTime( arg0.getUpdateTime() );
        if ( arg0.getTenantId() != null ) {
            appUser.setTenantId( String.valueOf( arg0.getTenantId() ) );
        }
        if ( arg0.getAvatar() != null ) {
            appUser.setAvatar( Long.parseLong( arg0.getAvatar() ) );
        }
        appUser.setChatIdentity( arg0.getChatIdentity() );
        appUser.setDeptId( arg0.getDeptId() );
        appUser.setEmail( arg0.getEmail() );
        appUser.setFaceUrl( arg0.getFaceUrl() );
        appUser.setNickName( arg0.getNickName() );
        appUser.setPassword( arg0.getPassword() );
        appUser.setPhoneNumber( arg0.getPhoneNumber() );
        appUser.setRemark( arg0.getRemark() );
        appUser.setRoleId( arg0.getRoleId() );
        appUser.setSalt( arg0.getSalt() );
        appUser.setSex( arg0.getSex() );
        appUser.setStatus( arg0.getStatus() );
        appUser.setSystemType( arg0.getSystemType() );
        appUser.setTaskId( arg0.getTaskId() );
        appUser.setUserId( arg0.getUserId() );
        appUser.setUserName( arg0.getUserName() );
        appUser.setUserType( arg0.getUserType() );

        return appUser;
    }

    @Override
    public AppUser convert(AppUserBo arg0, AppUser arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        if ( arg0.getTenantId() != null ) {
            arg1.setTenantId( String.valueOf( arg0.getTenantId() ) );
        }
        else {
            arg1.setTenantId( null );
        }
        if ( arg0.getAvatar() != null ) {
            arg1.setAvatar( Long.parseLong( arg0.getAvatar() ) );
        }
        else {
            arg1.setAvatar( null );
        }
        arg1.setChatIdentity( arg0.getChatIdentity() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setFaceUrl( arg0.getFaceUrl() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setPassword( arg0.getPassword() );
        arg1.setPhoneNumber( arg0.getPhoneNumber() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setRoleId( arg0.getRoleId() );
        arg1.setSalt( arg0.getSalt() );
        arg1.setSex( arg0.getSex() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setSystemType( arg0.getSystemType() );
        arg1.setTaskId( arg0.getTaskId() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setUserName( arg0.getUserName() );
        arg1.setUserType( arg0.getUserType() );

        return arg1;
    }
}
