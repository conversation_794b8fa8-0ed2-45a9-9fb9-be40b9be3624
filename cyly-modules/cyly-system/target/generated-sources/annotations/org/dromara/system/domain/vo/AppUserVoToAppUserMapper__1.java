package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppUser;
import org.dromara.system.domain.AppUserToAppUserVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppUserToAppUserVoMapper__1.class},
    imports = {}
)
public interface AppUserVoToAppUserMapper__1 extends BaseMapper<AppUserVo, AppUser> {
}
