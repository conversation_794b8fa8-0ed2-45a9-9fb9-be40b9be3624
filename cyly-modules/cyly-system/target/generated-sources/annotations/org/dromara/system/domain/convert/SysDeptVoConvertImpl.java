package org.dromara.system.domain.convert;

import javax.annotation.processing.Generated;
import org.dromara.system.api.domain.vo.RemoteDeptVo;
import org.dromara.system.domain.vo.SysDeptVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T09:56:26+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysDeptVoConvertImpl implements SysDeptVoConvert {

    @Override
    public RemoteDeptVo convert(SysDeptVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        RemoteDeptVo remoteDeptVo = new RemoteDeptVo();

        remoteDeptVo.setAncestors( arg0.getAncestors() );
        remoteDeptVo.setCreateTime( arg0.getCreateTime() );
        remoteDeptVo.setDeptCategory( arg0.getDeptCategory() );
        remoteDeptVo.setDeptId( arg0.getDeptId() );
        remoteDeptVo.setDeptName( arg0.getDeptName() );
        remoteDeptVo.setEmail( arg0.getEmail() );
        remoteDeptVo.setLeader( arg0.getLeader() );
        remoteDeptVo.setLeaderName( arg0.getLeaderName() );
        remoteDeptVo.setOrderNum( arg0.getOrderNum() );
        remoteDeptVo.setParentId( arg0.getParentId() );
        remoteDeptVo.setParentName( arg0.getParentName() );
        remoteDeptVo.setPhone( arg0.getPhone() );
        remoteDeptVo.setStatus( arg0.getStatus() );

        return remoteDeptVo;
    }

    @Override
    public RemoteDeptVo convert(SysDeptVo arg0, RemoteDeptVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAncestors( arg0.getAncestors() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeptCategory( arg0.getDeptCategory() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setDeptName( arg0.getDeptName() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setLeader( arg0.getLeader() );
        arg1.setLeaderName( arg0.getLeaderName() );
        arg1.setOrderNum( arg0.getOrderNum() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setParentName( arg0.getParentName() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
