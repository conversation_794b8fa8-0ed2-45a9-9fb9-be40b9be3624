package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysIpBanBoToSysIpBanMapper__1;
import org.dromara.system.domain.vo.SysIpBanVo;
import org.dromara.system.domain.vo.SysIpBanVoToSysIpBanMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {SysIpBanVoToSysIpBanMapper__1.class,SysIpBanBoToSysIpBanMapper__1.class},
    imports = {}
)
public interface SysIpBanToSysIpBanVoMapper__1 extends BaseMapper<SysIpBan, SysIpBanVo> {
}
