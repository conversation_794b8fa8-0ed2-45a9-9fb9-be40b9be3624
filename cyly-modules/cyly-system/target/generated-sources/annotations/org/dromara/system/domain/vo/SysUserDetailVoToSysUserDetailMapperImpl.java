package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysUserDetail;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:10+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysUserDetailVoToSysUserDetailMapperImpl implements SysUserDetailVoToSysUserDetailMapper {

    @Override
    public SysUserDetail convert(SysUserDetailVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserDetail sysUserDetail = new SysUserDetail();

        if ( arg0.getAge() != null ) {
            sysUserDetail.setAge( arg0.getAge().intValue() );
        }
        sysUserDetail.setBirthday( arg0.getBirthday() );
        sysUserDetail.setColleges( arg0.getColleges() );
        sysUserDetail.setContactsPhone( arg0.getContactsPhone() );
        sysUserDetail.setDegree( arg0.getDegree() );
        sysUserDetail.setEmergencyContacts( arg0.getEmergencyContacts() );
        sysUserDetail.setEthnic( arg0.getEthnic() );
        sysUserDetail.setIdCard( arg0.getIdCard() );
        if ( arg0.getMarriage() != null ) {
            sysUserDetail.setMarriage( arg0.getMarriage().intValue() );
        }
        sysUserDetail.setOrigin( arg0.getOrigin() );
        sysUserDetail.setRemark( arg0.getRemark() );
        sysUserDetail.setSkill( arg0.getSkill() );
        sysUserDetail.setSpecialized( arg0.getSpecialized() );
        sysUserDetail.setStatus( arg0.getStatus() );
        sysUserDetail.setUserId( arg0.getUserId() );
        sysUserDetail.setWorkExp( arg0.getWorkExp() );

        return sysUserDetail;
    }

    @Override
    public SysUserDetail convert(SysUserDetailVo arg0, SysUserDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        if ( arg0.getAge() != null ) {
            arg1.setAge( arg0.getAge().intValue() );
        }
        else {
            arg1.setAge( null );
        }
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setColleges( arg0.getColleges() );
        arg1.setContactsPhone( arg0.getContactsPhone() );
        arg1.setDegree( arg0.getDegree() );
        arg1.setEmergencyContacts( arg0.getEmergencyContacts() );
        arg1.setEthnic( arg0.getEthnic() );
        arg1.setIdCard( arg0.getIdCard() );
        if ( arg0.getMarriage() != null ) {
            arg1.setMarriage( arg0.getMarriage().intValue() );
        }
        else {
            arg1.setMarriage( null );
        }
        arg1.setOrigin( arg0.getOrigin() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSkill( arg0.getSkill() );
        arg1.setSpecialized( arg0.getSpecialized() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setWorkExp( arg0.getWorkExp() );

        return arg1;
    }
}
