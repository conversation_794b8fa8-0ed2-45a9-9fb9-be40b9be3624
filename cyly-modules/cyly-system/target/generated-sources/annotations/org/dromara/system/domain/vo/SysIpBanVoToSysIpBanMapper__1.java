package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysIpBan;
import org.dromara.system.domain.SysIpBanToSysIpBanVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {SysIpBanToSysIpBanVoMapper__1.class},
    imports = {}
)
public interface SysIpBanVoToSysIpBanMapper__1 extends BaseMapper<SysIpBanVo, SysIpBan> {
}
