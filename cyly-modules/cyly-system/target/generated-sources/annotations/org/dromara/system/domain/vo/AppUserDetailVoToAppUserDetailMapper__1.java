package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppUserDetail;
import org.dromara.system.domain.AppUserDetailToAppUserDetailVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppUserDetailToAppUserDetailVoMapper__1.class},
    imports = {}
)
public interface AppUserDetailVoToAppUserDetailMapper__1 extends BaseMapper<AppUserDetailVo, AppUserDetail> {
}
