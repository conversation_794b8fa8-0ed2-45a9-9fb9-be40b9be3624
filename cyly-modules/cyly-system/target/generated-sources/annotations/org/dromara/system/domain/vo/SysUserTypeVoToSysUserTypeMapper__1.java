package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysUserType;
import org.dromara.system.domain.SysUserTypeToSysUserTypeVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {SysUserTypeToSysUserTypeVoMapper__1.class},
    imports = {}
)
public interface SysUserTypeVoToSysUserTypeMapper__1 extends BaseMapper<SysUserTypeVo, SysUserType> {
}
