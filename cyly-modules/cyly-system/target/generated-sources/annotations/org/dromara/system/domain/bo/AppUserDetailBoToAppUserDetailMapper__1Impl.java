package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.AppUserDetail;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T09:56:28+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class AppUserDetailBoToAppUserDetailMapper__1Impl implements AppUserDetailBoToAppUserDetailMapper__1 {

    @Override
    public AppUserDetail convert(AppUserDetailBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppUserDetail appUserDetail = new AppUserDetail();

        appUserDetail.setCreateBy( arg0.getCreateBy() );
        appUserDetail.setCreateDept( arg0.getCreateDept() );
        appUserDetail.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            appUserDetail.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        appUserDetail.setSearchValue( arg0.getSearchValue() );
        appUserDetail.setUpdateBy( arg0.getUpdateBy() );
        appUserDetail.setUpdateTime( arg0.getUpdateTime() );
        appUserDetail.setAge( arg0.getAge() );
        appUserDetail.setAppUserName( arg0.getAppUserName() );
        appUserDetail.setBirthday( arg0.getBirthday() );
        appUserDetail.setColleges( arg0.getColleges() );
        appUserDetail.setContactsPhone( arg0.getContactsPhone() );
        appUserDetail.setDegree( arg0.getDegree() );
        appUserDetail.setDetailId( arg0.getDetailId() );
        appUserDetail.setEmergencyContacts( arg0.getEmergencyContacts() );
        appUserDetail.setEthnic( arg0.getEthnic() );
        appUserDetail.setIdCard( arg0.getIdCard() );
        appUserDetail.setMarriage( arg0.getMarriage() );
        appUserDetail.setName( arg0.getName() );
        appUserDetail.setOrigin( arg0.getOrigin() );
        appUserDetail.setPostId( arg0.getPostId() );
        appUserDetail.setRemark( arg0.getRemark() );
        appUserDetail.setSkill( arg0.getSkill() );
        appUserDetail.setSpecialized( arg0.getSpecialized() );
        appUserDetail.setStatus( arg0.getStatus() );
        appUserDetail.setWorkExp( arg0.getWorkExp() );

        return appUserDetail;
    }

    @Override
    public AppUserDetail convert(AppUserDetailBo arg0, AppUserDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAge( arg0.getAge() );
        arg1.setAppUserName( arg0.getAppUserName() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setColleges( arg0.getColleges() );
        arg1.setContactsPhone( arg0.getContactsPhone() );
        arg1.setDegree( arg0.getDegree() );
        arg1.setDetailId( arg0.getDetailId() );
        arg1.setEmergencyContacts( arg0.getEmergencyContacts() );
        arg1.setEthnic( arg0.getEthnic() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setMarriage( arg0.getMarriage() );
        arg1.setName( arg0.getName() );
        arg1.setOrigin( arg0.getOrigin() );
        arg1.setPostId( arg0.getPostId() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSkill( arg0.getSkill() );
        arg1.setSpecialized( arg0.getSpecialized() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setWorkExp( arg0.getWorkExp() );

        return arg1;
    }
}
