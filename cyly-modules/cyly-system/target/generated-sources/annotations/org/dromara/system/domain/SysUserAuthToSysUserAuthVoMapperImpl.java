package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.SysUserAuthVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:12+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysUserAuthToSysUserAuthVoMapperImpl implements SysUserAuthToSysUserAuthVoMapper {

    @Override
    public SysUserAuthVo convert(SysUserAuth arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserAuthVo sysUserAuthVo = new SysUserAuthVo();

        sysUserAuthVo.setAuthIds( arg0.getAuthIds() );
        sysUserAuthVo.setId( arg0.getId() );
        sysUserAuthVo.setUserId( arg0.getUserId() );

        return sysUserAuthVo;
    }

    @Override
    public SysUserAuthVo convert(SysUserAuth arg0, SysUserAuthVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAuthIds( arg0.getAuthIds() );
        arg1.setId( arg0.getId() );
        arg1.setUserId( arg0.getUserId() );

        return arg1;
    }
}
