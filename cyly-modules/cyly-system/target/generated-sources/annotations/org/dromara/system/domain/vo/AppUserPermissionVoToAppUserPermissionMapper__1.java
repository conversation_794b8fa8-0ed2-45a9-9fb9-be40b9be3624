package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppUserPermission;
import org.dromara.system.domain.AppUserPermissionToAppUserPermissionVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppUserPermissionToAppUserPermissionVoMapper__1.class},
    imports = {}
)
public interface AppUserPermissionVoToAppUserPermissionMapper__1 extends BaseMapper<AppUserPermissionVo, AppUserPermission> {
}
