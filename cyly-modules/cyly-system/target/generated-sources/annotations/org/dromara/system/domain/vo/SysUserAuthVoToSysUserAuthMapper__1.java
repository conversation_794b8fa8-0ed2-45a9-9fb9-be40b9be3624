package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysUserAuth;
import org.dromara.system.domain.SysUserAuthToSysUserAuthVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {SysUserAuthToSysUserAuthVoMapper__1.class},
    imports = {}
)
public interface SysUserAuthVoToSysUserAuthMapper__1 extends BaseMapper<SysUserAuthVo, SysUserAuth> {
}
