package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.DictAreaBoToDictAreaMapper__1;
import org.dromara.system.domain.vo.DictAreaFullVo;
import org.dromara.system.domain.vo.DictAreaFullVoToDictAreaMapper__1;
import org.dromara.system.domain.vo.DictAreaVoToDictAreaMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {DictAreaBoToDictAreaMapper__1.class,DictAreaVoToDictAreaMapper__1.class,DictAreaFullVoToDictAreaMapper__1.class,DictAreaToDictAreaVoMapper__1.class},
    imports = {}
)
public interface DictAreaToDictAreaFullVoMapper__1 extends BaseMapper<DictArea, DictAreaFullVo> {
}
