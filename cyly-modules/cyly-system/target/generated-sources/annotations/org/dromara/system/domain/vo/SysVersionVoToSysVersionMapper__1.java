package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysVersion;
import org.dromara.system.domain.SysVersionToSysVersionVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {SysVersionToSysVersionVoMapper__1.class},
    imports = {}
)
public interface SysVersionVoToSysVersionMapper__1 extends BaseMapper<SysVersionVo, SysVersion> {
}
