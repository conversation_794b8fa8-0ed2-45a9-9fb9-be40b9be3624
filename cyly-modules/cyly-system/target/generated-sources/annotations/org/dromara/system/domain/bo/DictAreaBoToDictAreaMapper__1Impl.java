package org.dromara.system.domain.bo;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.DictArea;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T09:56:28+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class DictAreaBoToDictAreaMapper__1Impl implements DictAreaBoToDictAreaMapper__1 {

    @Override
    public DictArea convert(DictAreaBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        DictArea dictArea = new DictArea();

        dictArea.setAncestors( arg0.getAncestors() );
        dictArea.setAreaId( arg0.getAreaId() );
        dictArea.setAreaName( arg0.getAreaName() );
        dictArea.setHigherAreaName( arg0.getHigherAreaName() );
        if ( arg0.getLevel() != null ) {
            dictArea.setLevel( arg0.getLevel().longValue() );
        }
        dictArea.setParentId( arg0.getParentId() );

        return dictArea;
    }

    @Override
    public DictArea convert(DictAreaBo arg0, DictArea arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAncestors( arg0.getAncestors() );
        arg1.setAreaId( arg0.getAreaId() );
        arg1.setAreaName( arg0.getAreaName() );
        arg1.setHigherAreaName( arg0.getHigherAreaName() );
        if ( arg0.getLevel() != null ) {
            arg1.setLevel( arg0.getLevel().longValue() );
        }
        else {
            arg1.setLevel( null );
        }
        arg1.setParentId( arg0.getParentId() );

        return arg1;
    }
}
