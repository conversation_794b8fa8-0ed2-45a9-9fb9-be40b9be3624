package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEventToAppOperLogBoMapper__1;
import org.dromara.system.domain.AppOperLog;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppOperLogBoToOperLogEventMapper__1.class,OperLogEventToAppOperLogBoMapper__1.class},
    imports = {}
)
public interface AppOperLogBoToAppOperLogMapper__1 extends BaseMapper<AppOperLogBo, AppOperLog> {
}
