package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppUserPermission;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {},
    imports = {}
)
public interface AppUserPermissionBoToAppUserPermissionMapper__1 extends BaseMapper<AppUserPermissionBo, AppUserPermission> {
}
