package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.DictAddress;
import org.dromara.system.domain.DictAddressToDictAddressVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {DictAddressToDictAddressVoMapper__1.class},
    imports = {}
)
public interface DictAddressVoToDictAddressMapper__1 extends BaseMapper<DictAddressVo, DictAddress> {
}
