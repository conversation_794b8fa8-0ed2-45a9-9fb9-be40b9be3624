package org.dromara.system.domain;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.vo.SysDeptVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:12+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysDeptToSysDeptVoMapperImpl implements SysDeptToSysDeptVoMapper {

    @Override
    public SysDeptVo convert(SysDept arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysDeptVo sysDeptVo = new SysDeptVo();

        sysDeptVo.setAddress( arg0.getAddress() );
        sysDeptVo.setAncestors( arg0.getAncestors() );
        sysDeptVo.setAreaId( arg0.getAreaId() );
        sysDeptVo.setCreateTime( arg0.getCreateTime() );
        sysDeptVo.setDeptCategory( arg0.getDeptCategory() );
        sysDeptVo.setDeptId( arg0.getDeptId() );
        sysDeptVo.setDeptName( arg0.getDeptName() );
        sysDeptVo.setEmail( arg0.getEmail() );
        sysDeptVo.setEvaluationServiceRange( arg0.getEvaluationServiceRange() );
        sysDeptVo.setLeader( arg0.getLeader() );
        sysDeptVo.setOrderNum( arg0.getOrderNum() );
        sysDeptVo.setParentId( arg0.getParentId() );
        sysDeptVo.setPhone( arg0.getPhone() );
        sysDeptVo.setStatus( arg0.getStatus() );

        return sysDeptVo;
    }

    @Override
    public SysDeptVo convert(SysDept arg0, SysDeptVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAddress( arg0.getAddress() );
        arg1.setAncestors( arg0.getAncestors() );
        arg1.setAreaId( arg0.getAreaId() );
        arg1.setCreateTime( arg0.getCreateTime() );
        arg1.setDeptCategory( arg0.getDeptCategory() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setDeptName( arg0.getDeptName() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setEvaluationServiceRange( arg0.getEvaluationServiceRange() );
        arg1.setLeader( arg0.getLeader() );
        arg1.setOrderNum( arg0.getOrderNum() );
        arg1.setParentId( arg0.getParentId() );
        arg1.setPhone( arg0.getPhone() );
        arg1.setStatus( arg0.getStatus() );

        return arg1;
    }
}
