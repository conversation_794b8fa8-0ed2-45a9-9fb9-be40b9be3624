package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppUserBoToAppUserMapper__1;
import org.dromara.system.domain.vo.AppUserVo;
import org.dromara.system.domain.vo.AppUserVoToAppUserMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppUserBoToAppUserMapper__1.class,AppUserVoToAppUserMapper__1.class},
    imports = {}
)
public interface AppUserToAppUserVoMapper__1 extends BaseMapper<AppUser, AppUserVo> {
}
