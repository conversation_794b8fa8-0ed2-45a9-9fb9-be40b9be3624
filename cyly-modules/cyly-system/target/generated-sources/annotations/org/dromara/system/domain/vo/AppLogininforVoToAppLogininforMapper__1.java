package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppLogininfor;
import org.dromara.system.domain.AppLogininforToAppLogininforVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppLogininforToAppLogininforVoMapper__1.class},
    imports = {}
)
public interface AppLogininforVoToAppLogininforMapper__1 extends BaseMapper<AppLogininforVo, AppLogininfor> {
}
