package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysUserAuthBoToSysUserAuthMapper__1;
import org.dromara.system.domain.vo.SysUserAuthVo;
import org.dromara.system.domain.vo.SysUserAuthVoToSysUserAuthMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {SysUserAuthVoToSysUserAuthMapper__1.class,SysUserAuthBoToSysUserAuthMapper__1.class},
    imports = {}
)
public interface SysUserAuthToSysUserAuthVoMapper__1 extends BaseMapper<SysUserAuth, SysUserAuthVo> {
}
