package org.dromara.system.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysVersion;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:12+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysVersionVoToSysVersionMapperImpl implements SysVersionVoToSysVersionMapper {

    @Override
    public SysVersion convert(SysVersionVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysVersion sysVersion = new SysVersion();

        sysVersion.setId( arg0.getId() );
        sysVersion.setSort( arg0.getSort() );
        sysVersion.setVersionCode( arg0.getVersionCode() );
        sysVersion.setVersionDetails( arg0.getVersionDetails() );
        sysVersion.setVersionName( arg0.getVersionName() );
        sysVersion.setVersionUrl( arg0.getVersionUrl() );

        return sysVersion;
    }

    @Override
    public SysVersion convert(SysVersionVo arg0, SysVersion arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setId( arg0.getId() );
        arg1.setSort( arg0.getSort() );
        arg1.setVersionCode( arg0.getVersionCode() );
        arg1.setVersionDetails( arg0.getVersionDetails() );
        arg1.setVersionName( arg0.getVersionName() );
        arg1.setVersionUrl( arg0.getVersionUrl() );

        return arg1;
    }
}
