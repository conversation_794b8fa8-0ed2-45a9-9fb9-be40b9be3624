package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppOperLogBoToAppOperLogMapper__1;
import org.dromara.system.domain.vo.AppOperLogVo;
import org.dromara.system.domain.vo.AppOperLogVoToAppOperLogMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppOperLogVoToAppOperLogMapper__1.class,AppOperLogBoToAppOperLogMapper__1.class},
    imports = {}
)
public interface AppOperLogToAppOperLogVoMapper__1 extends BaseMapper<AppOperLog, AppOperLogVo> {
}
