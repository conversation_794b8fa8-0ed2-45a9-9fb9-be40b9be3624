package org.dromara.system.domain.bo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.common.log.event.OperLogEvent;
import org.dromara.common.log.event.OperLogEventToAppOperLogBoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppOperLogBoToAppOperLogMapper__1.class,OperLogEventToAppOperLogBoMapper__1.class},
    imports = {}
)
public interface AppOperLogBoToOperLogEventMapper__1 extends BaseMapper<AppOperLogBo, OperLogEvent> {
}
