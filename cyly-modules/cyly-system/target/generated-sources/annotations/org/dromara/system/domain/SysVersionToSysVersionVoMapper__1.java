package org.dromara.system.domain;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.SysVersionBoToSysVersionMapper__1;
import org.dromara.system.domain.vo.SysVersionVo;
import org.dromara.system.domain.vo.SysVersionVoToSysVersionMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {SysVersionBoToSysVersionMapper__1.class,SysVersionVoToSysVersionMapper__1.class},
    imports = {}
)
public interface SysVersionToSysVersionVoMapper__1 extends BaseMapper<SysVersion, SysVersionVo> {
}
