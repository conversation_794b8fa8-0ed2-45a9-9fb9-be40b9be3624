package org.dromara.system.domain.bo;

import java.util.LinkedHashMap;
import java.util.Map;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.SysUserDetail;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T09:56:28+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class SysUserDetailBoToSysUserDetailMapper__1Impl implements SysUserDetailBoToSysUserDetailMapper__1 {

    @Override
    public SysUserDetail convert(SysUserDetailBo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        SysUserDetail sysUserDetail = new SysUserDetail();

        sysUserDetail.setCreateBy( arg0.getCreateBy() );
        sysUserDetail.setCreateDept( arg0.getCreateDept() );
        sysUserDetail.setCreateTime( arg0.getCreateTime() );
        Map<String, Object> map = arg0.getParams();
        if ( map != null ) {
            sysUserDetail.setParams( new LinkedHashMap<String, Object>( map ) );
        }
        sysUserDetail.setSearchValue( arg0.getSearchValue() );
        sysUserDetail.setUpdateBy( arg0.getUpdateBy() );
        sysUserDetail.setUpdateTime( arg0.getUpdateTime() );
        sysUserDetail.setAge( arg0.getAge() );
        sysUserDetail.setBirthday( arg0.getBirthday() );
        sysUserDetail.setColleges( arg0.getColleges() );
        sysUserDetail.setContactsPhone( arg0.getContactsPhone() );
        sysUserDetail.setDegree( arg0.getDegree() );
        sysUserDetail.setEmergencyContacts( arg0.getEmergencyContacts() );
        sysUserDetail.setEthnic( arg0.getEthnic() );
        sysUserDetail.setIdCard( arg0.getIdCard() );
        sysUserDetail.setMarriage( arg0.getMarriage() );
        sysUserDetail.setOrigin( arg0.getOrigin() );
        sysUserDetail.setRemark( arg0.getRemark() );
        sysUserDetail.setSkill( arg0.getSkill() );
        sysUserDetail.setSpecialized( arg0.getSpecialized() );
        sysUserDetail.setStatus( arg0.getStatus() );
        sysUserDetail.setUserId( arg0.getUserId() );
        sysUserDetail.setWorkExp( arg0.getWorkExp() );

        return sysUserDetail;
    }

    @Override
    public SysUserDetail convert(SysUserDetailBo arg0, SysUserDetail arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setCreateBy( arg0.getCreateBy() );
        arg1.setCreateDept( arg0.getCreateDept() );
        arg1.setCreateTime( arg0.getCreateTime() );
        if ( arg1.getParams() != null ) {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.getParams().clear();
                arg1.getParams().putAll( map );
            }
            else {
                arg1.setParams( null );
            }
        }
        else {
            Map<String, Object> map = arg0.getParams();
            if ( map != null ) {
                arg1.setParams( new LinkedHashMap<String, Object>( map ) );
            }
        }
        arg1.setSearchValue( arg0.getSearchValue() );
        arg1.setUpdateBy( arg0.getUpdateBy() );
        arg1.setUpdateTime( arg0.getUpdateTime() );
        arg1.setAge( arg0.getAge() );
        arg1.setBirthday( arg0.getBirthday() );
        arg1.setColleges( arg0.getColleges() );
        arg1.setContactsPhone( arg0.getContactsPhone() );
        arg1.setDegree( arg0.getDegree() );
        arg1.setEmergencyContacts( arg0.getEmergencyContacts() );
        arg1.setEthnic( arg0.getEthnic() );
        arg1.setIdCard( arg0.getIdCard() );
        arg1.setMarriage( arg0.getMarriage() );
        arg1.setOrigin( arg0.getOrigin() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSkill( arg0.getSkill() );
        arg1.setSpecialized( arg0.getSpecialized() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setWorkExp( arg0.getWorkExp() );

        return arg1;
    }
}
