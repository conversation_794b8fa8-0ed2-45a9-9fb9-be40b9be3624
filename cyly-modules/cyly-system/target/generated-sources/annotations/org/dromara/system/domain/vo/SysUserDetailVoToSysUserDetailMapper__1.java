package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.SysUserDetail;
import org.dromara.system.domain.SysUserDetailToSysUserDetailVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {SysUserDetailToSysUserDetailVoMapper__1.class},
    imports = {}
)
public interface SysUserDetailVoToSysUserDetailMapper__1 extends BaseMapper<SysUserDetailVo, SysUserDetail> {
}
