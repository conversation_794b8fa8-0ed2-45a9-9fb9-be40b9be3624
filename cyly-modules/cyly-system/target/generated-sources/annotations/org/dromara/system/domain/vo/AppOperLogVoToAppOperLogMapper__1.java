package org.dromara.system.domain.vo;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.AppOperLog;
import org.dromara.system.domain.AppOperLogToAppOperLogVoMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppOperLogToAppOperLogVoMapper__1.class},
    imports = {}
)
public interface AppOperLogVoToAppOperLogMapper__1 extends BaseMapper<AppOperLogVo, AppOperLog> {
}
