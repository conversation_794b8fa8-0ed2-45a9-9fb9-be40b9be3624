package org.dromara.common.log.event;

import java.util.Arrays;
import javax.annotation.processing.Generated;
import org.dromara.system.domain.bo.AppOperLogBo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:11+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class OperLogEventToAppOperLogBoMapperImpl implements OperLogEventToAppOperLogBoMapper {

    @Override
    public AppOperLogBo convert(OperLogEvent arg0) {
        if ( arg0 == null ) {
            return null;
        }

        AppOperLogBo appOperLogBo = new AppOperLogBo();

        appOperLogBo.setBusinessType( arg0.getBusinessType() );
        Integer[] businessTypes = arg0.getBusinessTypes();
        if ( businessTypes != null ) {
            appOperLogBo.setBusinessTypes( Arrays.copyOf( businessTypes, businessTypes.length ) );
        }
        appOperLogBo.setCostTime( arg0.getCostTime() );
        appOperLogBo.setDeptName( arg0.getDeptName() );
        appOperLogBo.setErrorMsg( arg0.getErrorMsg() );
        appOperLogBo.setJsonResult( arg0.getJsonResult() );
        appOperLogBo.setMethod( arg0.getMethod() );
        appOperLogBo.setOperId( arg0.getOperId() );
        appOperLogBo.setOperIp( arg0.getOperIp() );
        appOperLogBo.setOperLocation( arg0.getOperLocation() );
        appOperLogBo.setOperName( arg0.getOperName() );
        appOperLogBo.setOperParam( arg0.getOperParam() );
        appOperLogBo.setOperTime( arg0.getOperTime() );
        appOperLogBo.setOperUrl( arg0.getOperUrl() );
        appOperLogBo.setOperatorType( arg0.getOperatorType() );
        appOperLogBo.setRequestMethod( arg0.getRequestMethod() );
        appOperLogBo.setStatus( arg0.getStatus() );
        appOperLogBo.setTenantId( arg0.getTenantId() );
        appOperLogBo.setTitle( arg0.getTitle() );

        return appOperLogBo;
    }

    @Override
    public AppOperLogBo convert(OperLogEvent arg0, AppOperLogBo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setBusinessType( arg0.getBusinessType() );
        Integer[] businessTypes = arg0.getBusinessTypes();
        if ( businessTypes != null ) {
            arg1.setBusinessTypes( Arrays.copyOf( businessTypes, businessTypes.length ) );
        }
        else {
            arg1.setBusinessTypes( null );
        }
        arg1.setCostTime( arg0.getCostTime() );
        arg1.setDeptName( arg0.getDeptName() );
        arg1.setErrorMsg( arg0.getErrorMsg() );
        arg1.setJsonResult( arg0.getJsonResult() );
        arg1.setMethod( arg0.getMethod() );
        arg1.setOperId( arg0.getOperId() );
        arg1.setOperIp( arg0.getOperIp() );
        arg1.setOperLocation( arg0.getOperLocation() );
        arg1.setOperName( arg0.getOperName() );
        arg1.setOperParam( arg0.getOperParam() );
        arg1.setOperTime( arg0.getOperTime() );
        arg1.setOperUrl( arg0.getOperUrl() );
        arg1.setOperatorType( arg0.getOperatorType() );
        arg1.setRequestMethod( arg0.getRequestMethod() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setTenantId( arg0.getTenantId() );
        arg1.setTitle( arg0.getTitle() );

        return arg1;
    }
}
