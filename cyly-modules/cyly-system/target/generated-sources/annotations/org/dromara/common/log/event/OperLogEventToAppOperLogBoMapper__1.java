package org.dromara.common.log.event;

import io.github.linpeilie.AutoMapperConfig__550;
import io.github.linpeilie.BaseMapper;
import org.dromara.system.domain.bo.AppOperLogBo;
import org.dromara.system.domain.bo.AppOperLogBoToAppOperLogMapper__1;
import org.dromara.system.domain.bo.AppOperLogBoToOperLogEventMapper__1;
import org.dromara.system.domain.bo.SysOperLogBoToOperLogEventMapper__1;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__550.class,
    uses = {AppOperLogBoToAppOperLogMapper__1.class,AppOperLogBoToOperLogEventMapper__1.class,SysOperLogBoToOperLogEventMapper__1.class,OperLogEventToSysOperLogBoMapper__1.class},
    imports = {}
)
public interface OperLogEventToAppOperLogBoMapper__1 extends BaseMapper<OperLogEvent, AppOperLogBo> {
}
