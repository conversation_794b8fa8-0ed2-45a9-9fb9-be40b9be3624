package io.github.linpeilie;

import org.dromara.system.domain.convert.AppLogininforBoConvert;
import org.dromara.system.domain.convert.AppOperLogBoConvert;
import org.dromara.system.domain.convert.AppUserBoConvert;
import org.dromara.system.domain.convert.AppUserDetailBoConvert;
import org.dromara.system.domain.convert.AppUserDetailVoConvert;
import org.dromara.system.domain.convert.AppUserVoConvert;
import org.dromara.system.domain.convert.SysClientVoConvert;
import org.dromara.system.domain.convert.SysDeptVoConvert;
import org.dromara.system.domain.convert.SysDictDataVoConvert;
import org.dromara.system.domain.convert.SysLogininforBoConvert;
import org.dromara.system.domain.convert.SysOperLogBoConvert;
import org.dromara.system.domain.convert.SysSocialBoConvert;
import org.dromara.system.domain.convert.SysSocialVoConvert;
import org.dromara.system.domain.convert.SysTenantVoConvert;
import org.dromara.system.domain.convert.SysUserBoConvert;
import org.dromara.system.domain.convert.SysUserVoConvert;
import org.mapstruct.Builder;
import org.mapstruct.MapperConfig;
import org.mapstruct.ReportingPolicy;

@MapperConfig(
    componentModel = "spring-lazy",
    uses = {ConverterMapperAdapter__552.class, SysLogininforBoConvert.class, AppOperLogBoConvert.class, SysSocialBoConvert.class, AppLogininforBoConvert.class, AppUserVoConvert.class, SysDictDataVoConvert.class, SysTenantVoConvert.class, AppUserDetailBoConvert.class, SysOperLogBoConvert.class, SysUserVoConvert.class, SysClientVoConvert.class, AppUserDetailVoConvert.class, SysDeptVoConvert.class, SysSocialVoConvert.class, SysUserBoConvert.class, AppUserBoConvert.class},
    unmappedTargetPolicy = ReportingPolicy.IGNORE,
    builder = @Builder(buildMethod = "build", disableBuilder = true)
)
public interface AutoMapperConfig__552 {
}
