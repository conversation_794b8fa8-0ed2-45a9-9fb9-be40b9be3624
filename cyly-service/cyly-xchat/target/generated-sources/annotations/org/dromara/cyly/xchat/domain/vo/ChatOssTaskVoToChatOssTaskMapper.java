package org.dromara.cyly.xchat.domain.vo;

import io.github.linpeilie.AutoMapperConfig__551;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.xchat.domain.ChatOssTask;
import org.dromara.cyly.xchat.domain.ChatOssTaskToChatOssTaskVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__551.class,
    uses = {ChatOssTaskToChatOssTaskVoMapper.class},
    imports = {}
)
public interface ChatOssTaskVoToChatOssTaskMapper extends BaseMapper<ChatOssTaskVo, ChatOssTask> {
}
