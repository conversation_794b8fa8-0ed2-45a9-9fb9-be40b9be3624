package org.dromara.cyly.xchat.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.xchat.domain.vo.ChatLogVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:24+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatLogToChatLogVoMapperImpl implements ChatLogToChatLogVoMapper {

    @Override
    public ChatLogVo convert(ChatLog arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatLogVo chatLogVo = new ChatLogVo();

        chatLogVo.setClassType( arg0.getClassType() );
        chatLogVo.setContent( arg0.getContent() );
        chatLogVo.setExt( arg0.getExt() );
        chatLogVo.setFromAccount( arg0.getFromAccount() );
        chatLogVo.setFromDeviceId( arg0.getFromDeviceId() );
        chatLogVo.setFromNick( arg0.getFromNick() );
        chatLogVo.setId( arg0.getId() );
        chatLogVo.setIdClient( arg0.getIdClient() );
        chatLogVo.setIdServer( arg0.getIdServer() );
        chatLogVo.setScene( arg0.getScene() );
        chatLogVo.setStatus( arg0.getStatus() );
        chatLogVo.setToAccept( arg0.getToAccept() );
        chatLogVo.setToAvatar( arg0.getToAvatar() );
        chatLogVo.setToName( arg0.getToName() );
        chatLogVo.setType( arg0.getType() );

        return chatLogVo;
    }

    @Override
    public ChatLogVo convert(ChatLog arg0, ChatLogVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setClassType( arg0.getClassType() );
        arg1.setContent( arg0.getContent() );
        arg1.setExt( arg0.getExt() );
        arg1.setFromAccount( arg0.getFromAccount() );
        arg1.setFromDeviceId( arg0.getFromDeviceId() );
        arg1.setFromNick( arg0.getFromNick() );
        arg1.setId( arg0.getId() );
        arg1.setIdClient( arg0.getIdClient() );
        arg1.setIdServer( arg0.getIdServer() );
        arg1.setScene( arg0.getScene() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setToAccept( arg0.getToAccept() );
        arg1.setToAvatar( arg0.getToAvatar() );
        arg1.setToName( arg0.getToName() );
        arg1.setType( arg0.getType() );

        return arg1;
    }
}
