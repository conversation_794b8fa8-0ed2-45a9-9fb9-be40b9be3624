package org.dromara.cyly.xchat.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.xchat.domain.ChatLog;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:24+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatLogVoToChatLogMapperImpl implements ChatLogVoToChatLogMapper {

    @Override
    public ChatLog convert(ChatLogVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatLog chatLog = new ChatLog();

        chatLog.setClassType( arg0.getClassType() );
        chatLog.setContent( arg0.getContent() );
        chatLog.setExt( arg0.getExt() );
        chatLog.setFromAccount( arg0.getFromAccount() );
        chatLog.setFromDeviceId( arg0.getFromDeviceId() );
        chatLog.setFromNick( arg0.getFromNick() );
        chatLog.setId( arg0.getId() );
        chatLog.setIdClient( arg0.getIdClient() );
        chatLog.setIdServer( arg0.getIdServer() );
        chatLog.setScene( arg0.getScene() );
        chatLog.setStatus( arg0.getStatus() );
        chatLog.setToAccept( arg0.getToAccept() );
        chatLog.setToAvatar( arg0.getToAvatar() );
        chatLog.setToName( arg0.getToName() );
        chatLog.setType( arg0.getType() );

        return chatLog;
    }

    @Override
    public ChatLog convert(ChatLogVo arg0, ChatLog arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setClassType( arg0.getClassType() );
        arg1.setContent( arg0.getContent() );
        arg1.setExt( arg0.getExt() );
        arg1.setFromAccount( arg0.getFromAccount() );
        arg1.setFromDeviceId( arg0.getFromDeviceId() );
        arg1.setFromNick( arg0.getFromNick() );
        arg1.setId( arg0.getId() );
        arg1.setIdClient( arg0.getIdClient() );
        arg1.setIdServer( arg0.getIdServer() );
        arg1.setScene( arg0.getScene() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setToAccept( arg0.getToAccept() );
        arg1.setToAvatar( arg0.getToAvatar() );
        arg1.setToName( arg0.getToName() );
        arg1.setType( arg0.getType() );

        return arg1;
    }
}
