package org.dromara.cyly.xchat.domain;

import io.github.linpeilie.AutoMapperConfig__551;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.xchat.domain.bo.ChatOssTaskBoToChatOssTaskMapper;
import org.dromara.cyly.xchat.domain.vo.ChatOssTaskVo;
import org.dromara.cyly.xchat.domain.vo.ChatOssTaskVoToChatOssTaskMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__551.class,
    uses = {ChatOssTaskVoToChatOssTaskMapper.class,ChatOssTaskBoToChatOssTaskMapper.class},
    imports = {}
)
public interface ChatOssTaskToChatOssTaskVoMapper extends BaseMapper<ChatOssTask, ChatOssTaskVo> {
}
