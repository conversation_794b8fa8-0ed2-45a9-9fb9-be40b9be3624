package org.dromara.cyly.xchat.domain.vo;

import io.github.linpeilie.AutoMapperConfig__551;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.xchat.domain.UserNurseInfo;
import org.dromara.cyly.xchat.domain.UserNurseInfoToUserNurseInfoVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__551.class,
    uses = {UserNurseInfoToUserNurseInfoVoMapper.class},
    imports = {}
)
public interface UserNurseInfoVoToUserNurseInfoMapper extends BaseMapper<UserNurseInfoVo, UserNurseInfo> {
}
