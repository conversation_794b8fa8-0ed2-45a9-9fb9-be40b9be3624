package org.dromara.cyly.xchat.domain.vo;

import io.github.linpeilie.AutoMapperConfig__551;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.xchat.domain.ChatUserIm;
import org.dromara.cyly.xchat.domain.ChatUserImToChatUserImVoMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__551.class,
    uses = {ChatUserImToChatUserImVoMapper.class},
    imports = {}
)
public interface ChatUserImVoToChatUserImMapper extends BaseMapper<ChatUserImVo, ChatUserIm> {
}
