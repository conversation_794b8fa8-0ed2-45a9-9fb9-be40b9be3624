package org.dromara.cyly.xchat.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.xchat.domain.vo.ChatOssTaskVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:24+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatOssTaskToChatOssTaskVoMapperImpl implements ChatOssTaskToChatOssTaskVoMapper {

    @Override
    public ChatOssTaskVo convert(ChatOssTask arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatOssTaskVo chatOssTaskVo = new ChatOssTaskVo();

        chatOssTaskVo.setOssId( arg0.getOssId() );
        chatOssTaskVo.setTaskId( arg0.getTaskId() );

        return chatOssTaskVo;
    }

    @Override
    public ChatOssTaskVo convert(ChatOssTask arg0, ChatOssTaskVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setOssId( arg0.getOssId() );
        arg1.setTaskId( arg0.getTaskId() );

        return arg1;
    }
}
