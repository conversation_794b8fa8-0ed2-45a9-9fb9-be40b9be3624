package org.dromara.cyly.xchat.domain;

import javax.annotation.processing.Generated;
import org.dromara.cyly.xchat.domain.vo.ChatUserImVo;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:24+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatUserImToChatUserImVoMapperImpl implements ChatUserImToChatUserImVoMapper {

    @Override
    public ChatUserImVo convert(ChatUserIm arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatUserImVo chatUserImVo = new ChatUserImVo();

        chatUserImVo.setAccid( arg0.getAccid() );
        chatUserImVo.setBirth( arg0.getBirth() );
        chatUserImVo.setDelFlag( arg0.getDelFlag() );
        chatUserImVo.setDeptId( arg0.getDeptId() );
        chatUserImVo.setEmail( arg0.getEmail() );
        chatUserImVo.setEx( arg0.getEx() );
        chatUserImVo.setGender( arg0.getGender() );
        chatUserImVo.setIcon( arg0.getIcon() );
        chatUserImVo.setLoginDate( arg0.getLoginDate() );
        chatUserImVo.setLoginIp( arg0.getLoginIp() );
        chatUserImVo.setMobile( arg0.getMobile() );
        chatUserImVo.setName( arg0.getName() );
        chatUserImVo.setNickName( arg0.getNickName() );
        chatUserImVo.setPassword( arg0.getPassword() );
        chatUserImVo.setRemark( arg0.getRemark() );
        chatUserImVo.setSign( arg0.getSign() );
        chatUserImVo.setStatus( arg0.getStatus() );
        chatUserImVo.setToken( arg0.getToken() );
        chatUserImVo.setUserId( arg0.getUserId() );
        chatUserImVo.setUserType( arg0.getUserType() );

        return chatUserImVo;
    }

    @Override
    public ChatUserImVo convert(ChatUserIm arg0, ChatUserImVo arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setAccid( arg0.getAccid() );
        arg1.setBirth( arg0.getBirth() );
        arg1.setDelFlag( arg0.getDelFlag() );
        arg1.setDeptId( arg0.getDeptId() );
        arg1.setEmail( arg0.getEmail() );
        arg1.setEx( arg0.getEx() );
        arg1.setGender( arg0.getGender() );
        arg1.setIcon( arg0.getIcon() );
        arg1.setLoginDate( arg0.getLoginDate() );
        arg1.setLoginIp( arg0.getLoginIp() );
        arg1.setMobile( arg0.getMobile() );
        arg1.setName( arg0.getName() );
        arg1.setNickName( arg0.getNickName() );
        arg1.setPassword( arg0.getPassword() );
        arg1.setRemark( arg0.getRemark() );
        arg1.setSign( arg0.getSign() );
        arg1.setStatus( arg0.getStatus() );
        arg1.setToken( arg0.getToken() );
        arg1.setUserId( arg0.getUserId() );
        arg1.setUserType( arg0.getUserType() );

        return arg1;
    }
}
