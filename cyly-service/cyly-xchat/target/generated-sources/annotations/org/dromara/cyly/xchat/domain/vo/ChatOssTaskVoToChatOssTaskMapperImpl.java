package org.dromara.cyly.xchat.domain.vo;

import javax.annotation.processing.Generated;
import org.dromara.cyly.xchat.domain.ChatOssTask;
import org.springframework.stereotype.Component;

@Generated(
    value = "org.mapstruct.ap.MappingProcessor",
    date = "2025-06-08T00:00:24+0800",
    comments = "version: 1.5.5.Final, compiler: Eclipse JDT (IDE) 3.42.0.v20250514-1000, environment: Java 21.0.7 (Eclipse Adoptium)"
)
@Component
public class ChatOssTaskVoToChatOssTaskMapperImpl implements ChatOssTaskVoToChatOssTaskMapper {

    @Override
    public ChatOssTask convert(ChatOssTaskVo arg0) {
        if ( arg0 == null ) {
            return null;
        }

        ChatOssTask chatOssTask = new ChatOssTask();

        chatOssTask.setOssId( arg0.getOssId() );
        chatOssTask.setTaskId( arg0.getTaskId() );

        return chatOssTask;
    }

    @Override
    public ChatOssTask convert(ChatOssTaskVo arg0, ChatOssTask arg1) {
        if ( arg0 == null ) {
            return arg1;
        }

        arg1.setOssId( arg0.getOssId() );
        arg1.setTaskId( arg0.getTaskId() );

        return arg1;
    }
}
