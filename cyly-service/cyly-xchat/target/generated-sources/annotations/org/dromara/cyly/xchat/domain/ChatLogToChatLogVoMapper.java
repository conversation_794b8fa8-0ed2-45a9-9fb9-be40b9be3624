package org.dromara.cyly.xchat.domain;

import io.github.linpeilie.AutoMapperConfig__551;
import io.github.linpeilie.BaseMapper;
import org.dromara.cyly.xchat.domain.bo.ChatLogBoToChatLogMapper;
import org.dromara.cyly.xchat.domain.vo.ChatLogVo;
import org.dromara.cyly.xchat.domain.vo.ChatLogVoToChatLogMapper;
import org.mapstruct.Mapper;

@Mapper(
    config = AutoMapperConfig__551.class,
    uses = {ChatLogVoToChatLogMapper.class,ChatLogBoToChatLogMapper.class},
    imports = {}
)
public interface ChatLogToChatLogVoMapper extends BaseMapper<ChatLog, ChatLogVo> {
}
